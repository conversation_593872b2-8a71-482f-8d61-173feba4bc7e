package services

import (
	"testing"

	"adgitops-ui/src/backend/models"

	"github.com/stretchr/testify/assert"
)

// TestMemberTypeResolution tests that group members are correctly classified as groups or users
func TestMemberTypeResolution(t *testing.T) {
	dp := &DataProcessor{}

	// Create test groups with nested group structure
	groups := []models.Group{
		{
			Groupname:   "parent_group",
			Type:        "security",
			Description: "Parent group containing other groups",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "child_group", Type: models.UserMemberType}, // Initially marked as user (simulating JSON parsing)
				{Name: "john", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "child_group",
			Type:        "technical",
			Description: "Child group with users",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "alice", Type: models.UserMemberType},
				{Name: "bob", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "another_group",
			Type:        "business",
			Description: "Another group",
			Lob:         "finance",
			Members: models.GroupMembers{
				{Name: "charlie", Type: models.UserMemberType},
			},
		},
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Verify parent_group has correct member types
	parentGroup := groups[0]
	assert.Equal(t, "parent_group", parentGroup.Groupname)
	assert.Len(t, parentGroup.Members, 2, "Parent group should have exactly 2 direct members")

	// Find the child_group member
	var childGroupMember *models.Member
	var johnMember *models.Member
	for i, member := range parentGroup.Members {
		if member.Name == "child_group" {
			childGroupMember = &parentGroup.Members[i]
		} else if member.Name == "john" {
			johnMember = &parentGroup.Members[i]
		}
	}

	// Verify child_group is correctly identified as a group
	assert.NotNil(t, childGroupMember, "child_group should be found in parent_group members")
	assert.Equal(t, models.GroupMemberType, childGroupMember.Type, "child_group should be marked as type 'group'")

	// Verify john is correctly identified as a user
	assert.NotNil(t, johnMember, "john should be found in parent_group members")
	assert.Equal(t, models.UserMemberType, johnMember.Type, "john should be marked as type 'user'")

	// Verify that users from child_group are NOT added to parent_group
	for _, member := range parentGroup.Members {
		assert.NotEqual(t, "alice", member.Name, "alice should not be a direct member of parent_group")
		assert.NotEqual(t, "bob", member.Name, "bob should not be a direct member of parent_group")
	}

	// Verify child_group still has its own members
	childGroup := groups[1]
	assert.Equal(t, "child_group", childGroup.Groupname)
	assert.Len(t, childGroup.Members, 2, "Child group should have exactly 2 members")

	// Verify child_group members are all users
	for _, member := range childGroup.Members {
		assert.Equal(t, models.UserMemberType, member.Type, "All members of child_group should be users")
	}
}

// TestMemberTypeWithGroupPrefix tests that groups with "group:" prefix are handled correctly
func TestMemberTypeWithGroupPrefix(t *testing.T) {
	dp := &DataProcessor{}

	// Create test groups where one group references another with "group:" prefix
	groups := []models.Group{
		{
			Groupname:   "main_group",
			Type:        "security",
			Description: "Main group",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "sub_group", Type: models.GroupMemberType}, // Already marked as group (from "group:" prefix)
				{Name: "direct_user", Type: models.UserMemberType},
			},
		},
		{
			Groupname:   "sub_group",
			Type:        "technical",
			Description: "Sub group",
			Lob:         "it",
			Members: models.GroupMembers{
				{Name: "nested_user", Type: models.UserMemberType},
			},
		},
	}

	// Resolve members
	dp.ResolveMembers(groups)

	// Verify main_group has correct member types
	mainGroup := groups[0]
	assert.Len(t, mainGroup.Members, 2, "Main group should have exactly 2 direct members")

	// Verify sub_group is still marked as group
	var subGroupMember *models.Member
	for i, member := range mainGroup.Members {
		if member.Name == "sub_group" {
			subGroupMember = &mainGroup.Members[i]
			break
		}
	}
	assert.NotNil(t, subGroupMember, "sub_group should be found in main_group members")
	assert.Equal(t, models.GroupMemberType, subGroupMember.Type, "sub_group should remain marked as type 'group'")

	// Verify nested_user is NOT added to main_group
	for _, member := range mainGroup.Members {
		assert.NotEqual(t, "nested_user", member.Name, "nested_user should not be a direct member of main_group")
	}
}
