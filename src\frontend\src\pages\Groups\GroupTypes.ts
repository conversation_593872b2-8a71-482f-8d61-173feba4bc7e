// Group member interface
export interface GroupMember {
  name: string;
  type: string;
}

// Enhanced membership information interfaces
export interface MembershipInfo {
  users: string[];
  groups: string[];
  total: number;
}

export interface ResolvedUser {
  name: string;
  path: string[];
  direct: boolean;
}

export interface ResolvedMembers {
  users: ResolvedUser[];
  total: number;
}

// Group interface
export interface Group {
  Groupname: string;
  Type: string;
  Description: string;
  Members: string[] | GroupMember[] | Record<string, any>;

  // Enhanced membership information (optional for backward compatibility)
  DirectMembers?: MembershipInfo;
  ResolvedMembers?: ResolvedMembers;
  ParentGroups?: string[];

  LOB?: string;     // Make LOB optional since it may not exist in API response
  Lob?: string;     // Alternative casing
  lob?: string;     // Alternative casing
  Lobs?: string[];  // Possible array format
  [key: string]: any; // Allow dynamic key indexing
}

// Pagination information interface
export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

// Request parameters for fetching groups
export interface GroupRequestParams {
  repoId: string;
  page: number;
  pageSize: number;
  lob: string;
  search?: string;
}
