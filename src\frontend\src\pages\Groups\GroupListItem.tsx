import React from 'react';
import { Info, ToggleLeft, ToggleRight } from 'lucide-react';
import { Group, PaginationInfo } from './GroupTypes';
import GroupMemberItem from './GroupMemberItem';
import GroupMembershipInfo from './GroupMembershipInfo';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface GroupListItemProps {
  group: Group;
  groupKey: string;
  expandedRows: Record<string, boolean>;
  toggleRowExpansion: (groupName: string) => void;
  selectedRepoId: string | null;
  selectedLob: string;
  pagination: PaginationInfo;
  setInputValue: (value: string) => void;
  fetchGroupsData: (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => void;
}

const GroupListItem = ({
  group,
  groupKey,
  expandedRows,
  toggleRowExpansion,
  selectedRepoId,
  selectedLob,
  pagination,
  setInputValue,
  fetchGroupsData
}: GroupListItemProps) => {
  const [useEnhancedView, setUseEnhancedView] = React.useState(false);

  // Handle navigation to another group
  const handleGroupClick = (groupName: string) => {
    if (selectedRepoId) {
      setInputValue(`groupname:${groupName}`);
      fetchGroupsData(selectedRepoId, 1, pagination.pageSize, selectedLob, `groupname:${groupName}`);
    }
  };
  // Normalize members to array of objects with name and type
  const normalizeMembers = () => {
    if (!group.Members) return [];

    if (Array.isArray(group.Members)) {
      return group.Members.map(member => {
        if (typeof member === 'string') {
          return { name: member, type: 'user' };
        }
        return member;
      });
    } else if (typeof group.Members === 'object') {
      return Object.entries(group.Members).map(([name, type]) => ({
        name,
        type: typeof type === 'string' ? type : 'user'
      }));
    }
    return [];
  };

  const members = normalizeMembers();

  // Get LOB value from any of the possible fields
  const getLobValue = () => {
    if (group.LOB) return group.LOB;
    if (group.Lob) return group.Lob;
    if (group.lob) return group.lob;
    if (group.Lobs && Array.isArray(group.Lobs) && group.Lobs.length > 0) {
      return group.Lobs.join(', ');
    }
    return '';
  };

  const lobValue = getLobValue();

  return (
    <div className="grid grid-cols-4 gap-4 py-4 px-4 border-b border-gray-200 dark:border-gray-700">
      <div className="text-sm font-medium">{group.Groupname}</div>
      <div className="text-sm">
        {group.Type || <span className="text-gray-400 text-xs">No type</span>}
      </div>
      <div className="text-sm">
        {lobValue ? (
          <div className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs inline-block">
            {lobValue}
          </div>
        ) : (
          <span className="text-gray-400 text-xs">No LOB</span>
        )}
      </div>
      <div className="text-sm">
        {group.Description || group.SourceFile ? (
          <div className="flex flex-col gap-1">
            {group.Description && (
              <div className="flex items-center">
                <Popover>
                  <PopoverTrigger>
                    <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                  </PopoverTrigger>
                  <PopoverContent side="top">
                    <div className="p-2 max-w-md">
                      <p className="text-sm">
                          <span className="font-medium">Description</span>
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          {group.Description}
                        </div>
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>
                <span className="ml-2 text-xs text-gray-500 truncate max-w-[200px]">
                  {group.Description}
                </span>
              </div>
            )}
            {group.SourceFile && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Source File:</span>
                <span className="ml-1 bg-blue-50 text-blue-700 px-1.5 py-0.5 rounded text-xs">
                  {group.SourceFile}
                </span>
              </div>
            )}
          </div>
        ) : (
          <span className="text-gray-400 text-xs">No description</span>
        )}
      </div>
      <div className="col-span-4">
        {members && members.length > 0 ? (
          <div className="flex flex-col gap-3">
            {/* Group members section */}
            <div>
              <div className="text-xs font-medium text-gray-500 mb-1">Group Members:</div>
              <div className="flex flex-wrap gap-1 items-center">
                {/* Filter and show group members */}
                {members.filter(member => {
                  const memberType = typeof member === 'string' ? 'user' : member.type;
                  return memberType === 'group';
                }).length > 0 ? (
                  members
                    .filter(member => {
                      const memberType = typeof member === 'string' ? 'user' : member.type;
                      return memberType === 'group';
                    })
                    .slice(0, 5)
                    .map((member, memberIndex) => {
                      const memberName = typeof member === 'string' ? member : member.name;
                      return (
                        <GroupMemberItem
                          key={`${groupKey}-group-${memberName}-${memberIndex}`}
                          memberName={memberName}
                          memberType="group"
                          groupKey={groupKey}
                          memberIndex={memberIndex}
                          selectedRepoId={selectedRepoId}
                          selectedLob={selectedLob}
                          pagination={pagination}
                          setInputValue={setInputValue}
                          fetchGroupsData={fetchGroupsData}
                        />
                      );
                    })
                ) : (
                  <div className="text-gray-400 text-xs">No group members</div>
                )}

                {/* Show +X more for groups if needed */}
                {members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'group').length > 5 && (
                  <div
                    className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                    onClick={() => toggleRowExpansion(group.Groupname + '-groups')}
                  >
                    {expandedRows[group.Groupname + '-groups'] ?
                      <span>Show less</span> :
                      <span>+{members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'group').length - 5} more</span>
                    }
                  </div>
                )}
              </div>

              {/* Expanded group members */}
              {expandedRows[group.Groupname + '-groups'] && members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'group').length > 5 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {members
                    .filter(member => {
                      const memberType = typeof member === 'string' ? 'user' : member.type;
                      return memberType === 'group';
                    })
                    .slice(5)
                    .map((member, memberIndex) => {
                      const memberName = typeof member === 'string' ? member : member.name;
                      return (
                        <GroupMemberItem
                          key={`${groupKey}-group-${memberName}-${memberIndex}-expanded`}
                          memberName={memberName}
                          memberType="group"
                          groupKey={groupKey}
                          memberIndex={memberIndex}
                          expanded={true}
                          selectedRepoId={selectedRepoId}
                          selectedLob={selectedLob}
                          pagination={pagination}
                          setInputValue={setInputValue}
                          fetchGroupsData={fetchGroupsData}
                        />
                      );
                    })}
                </div>
              )}
            </div>

            {/* User members section */}
            <div>
              <div className="text-xs font-medium text-gray-500 mb-1">User Members:</div>
              <div className="flex flex-wrap gap-1 items-center">
                {/* Filter and show user members */}
                {members.filter(member => {
                  const memberType = typeof member === 'string' ? 'user' : member.type;
                  return memberType === 'user';
                }).length > 0 ? (
                  members
                    .filter(member => {
                      const memberType = typeof member === 'string' ? 'user' : member.type;
                      return memberType === 'user';
                    })
                    .slice(0, 5)
                    .map((member, memberIndex) => {
                      const memberName = typeof member === 'string' ? member : member.name;
                      return (
                        <GroupMemberItem
                          key={`${groupKey}-user-${memberName}-${memberIndex}`}
                          memberName={memberName}
                          memberType="user"
                          groupKey={groupKey}
                          memberIndex={memberIndex}
                          selectedRepoId={selectedRepoId}
                          selectedLob={selectedLob}
                          pagination={pagination}
                          setInputValue={setInputValue}
                          fetchGroupsData={fetchGroupsData}
                        />
                      );
                    })
                ) : (
                  <div className="text-gray-400 text-xs">No user members</div>
                )}

                {/* Show +X more for users if needed */}
                {members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'user').length > 5 && (
                  <div
                    className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs cursor-pointer inline-block h-5 leading-4 ml-1"
                    onClick={() => toggleRowExpansion(group.Groupname + '-users')}
                  >
                    {expandedRows[group.Groupname + '-users'] ?
                      <span>Show less</span> :
                      <span>+{members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'user').length - 5} more</span>
                    }
                  </div>
                )}
              </div>

              {/* Expanded user members */}
              {expandedRows[group.Groupname + '-users'] && members.filter(m => (typeof m === 'string' ? 'user' : m.type) === 'user').length > 5 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {members
                    .filter(member => {
                      const memberType = typeof member === 'string' ? 'user' : member.type;
                      return memberType === 'user';
                    })
                    .slice(5)
                    .map((member, memberIndex) => {
                      const memberName = typeof member === 'string' ? member : member.name;
                      return (
                        <GroupMemberItem
                          key={`${groupKey}-user-${memberName}-${memberIndex}-expanded`}
                          memberName={memberName}
                          memberType="user"
                          groupKey={groupKey}
                          memberIndex={memberIndex}
                          expanded={true}
                          selectedRepoId={selectedRepoId}
                          selectedLob={selectedLob}
                          pagination={pagination}
                          setInputValue={setInputValue}
                          fetchGroupsData={fetchGroupsData}
                        />
                      );
                    })}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-3">
            <div>
              <div className="text-xs font-medium text-gray-500 mb-1">Group Members:</div>
              <div className="text-gray-400 text-xs">No group members</div>
            </div>
            <div>
              <div className="text-xs font-medium text-gray-500 mb-1">User Members:</div>
              <div className="text-gray-400 text-xs">No user members</div>
            </div>
          </div>
        )}

        {/* Enhanced View Toggle and Display */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Membership View</span>
            <button
              onClick={() => setUseEnhancedView(!useEnhancedView)}
              className="flex items-center gap-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              {useEnhancedView ? (
                <>
                  <ToggleRight className="w-4 h-4 text-blue-600" />
                  Enhanced
                </>
              ) : (
                <>
                  <ToggleLeft className="w-4 h-4 text-gray-400" />
                  Basic
                </>
              )}
            </button>
          </div>

          {useEnhancedView && (
            <GroupMembershipInfo
              group={group}
              onGroupClick={handleGroupClick}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default GroupListItem;
