import React, { useState, useEffect } from 'react';
import { Users, UserCheck, Building2, ChevronDown, ChevronRight, Loader2 } from 'lucide-react';
import { Group, ResolvedUser } from './GroupTypes';
import { apiClient } from '@/api/client';

interface GroupMembershipInfoProps {
  group: Group;
  repoId: string;
  onGroupClick?: (groupName: string) => void;
}

const GroupMembershipInfo: React.FC<GroupMembershipInfoProps> = ({
  group,
  repoId,
  onGroupClick
}) => {
  const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'parents'>('direct');
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [enhancedGroup, setEnhancedGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create enhanced data from current group data
  useEffect(() => {
    const createEnhancedData = () => {
      setLoading(true);
      setError(null);

      try {
        // Create enhanced data from the existing Members array
        const directUsers: string[] = [];
        const directGroups: string[] = [];

        // Process members to categorize them
        if (group.Members && Array.isArray(group.Members)) {
          group.Members.forEach(member => {
            const memberName = typeof member === 'string' ? member : member.name;
            const memberType = typeof member === 'string' ? 'user' : member.type;

            if (memberType === 'group') {
              directGroups.push(memberName);
            } else {
              directUsers.push(memberName);
            }
          });
        }

        // Create the enhanced group object
        const enhanced: Group = {
          ...group,
          DirectMembers: {
            users: directUsers,
            groups: directGroups,
            total: directUsers.length + directGroups.length
          },
          ResolvedMembers: {
            users: directUsers.map(user => ({
              name: user,
              path: [group.Groupname],
              direct: true
            })),
            total: directUsers.length
          },
          ParentGroups: [] // We don't have parent group information in the current data
        };

        console.log('Enhanced group data created:', enhanced);
        setEnhancedGroup(enhanced);
      } catch (err) {
        console.error('Failed to create enhanced group data:', err);
        setError('Failed to process membership data');
      } finally {
        setLoading(false);
      }
    };

    createEnhancedData();
  }, [group]);

  const togglePath = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const renderDirectMembers = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!enhancedGroup?.DirectMembers) {
      return <div className="text-gray-500 text-sm">No enhanced membership data available</div>;
    }

    const { users, groups, total } = enhancedGroup.DirectMembers;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>Total: {total} members ({users.length} users, {groups.length} groups)</span>
        </div>

        {users.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <UserCheck className="w-4 h-4" />
              Direct Users ({users.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {users.map((user) => (
                <div key={user} className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">
                  {user}
                </div>
              ))}
            </div>
          </div>
        )}

        {groups.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <Building2 className="w-4 h-4" />
              Direct Groups ({groups.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {groups.map((groupName) => (
                <div
                  key={groupName}
                  className="px-2 py-1 bg-green-50 text-green-700 rounded text-sm cursor-pointer hover:bg-green-100"
                  onClick={() => onGroupClick?.(groupName)}
                >
                  {groupName}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderResolvedMembers = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!enhancedGroup?.ResolvedMembers) {
      return <div className="text-gray-500 text-sm">No resolved membership data available</div>;
    }

    const { users, total } = enhancedGroup.ResolvedMembers;

    if (users.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          No direct users found. This group may contain only other groups as members.
          <br />
          <span className="text-xs">Note: Nested user resolution from sub-groups is not yet implemented.</span>
        </div>
      );
    }

    // Group users by their access path
    const usersByPath = users.reduce((acc, user) => {
      const pathKey = user.path.join(' → ');
      if (!acc[pathKey]) {
        acc[pathKey] = [];
      }
      acc[pathKey].push(user);
      return acc;
    }, {} as Record<string, ResolvedUser[]>);

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>Total: {total} users with access</span>
        </div>

        <div className="space-y-2">
          {Object.entries(usersByPath).map(([pathKey, pathUsers]) => {
            const isExpanded = expandedPaths.has(pathKey);
            const isDirect = pathUsers[0]?.direct;

            return (
              <div key={pathKey} className="border rounded-lg">
                <div
                  className={`p-3 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                    isDirect ? 'bg-blue-50' : 'bg-gray-50'
                  }`}
                  onClick={() => togglePath(pathKey)}
                >
                  <div className="flex items-center gap-2">
                    {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                    <span className="text-sm font-medium">
                      {pathKey} {isDirect && <span className="text-blue-600">(Direct)</span>}
                    </span>
                    <span className="text-xs text-gray-500">({pathUsers.length} users)</span>
                  </div>
                </div>

                {isExpanded && (
                  <div className="p-3 border-t bg-white">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
                      {pathUsers.map((user) => (
                        <div
                          key={user.name}
                          className={`px-2 py-1 rounded text-sm ${
                            user.direct
                              ? 'bg-blue-50 text-blue-700'
                              : 'bg-gray-50 text-gray-700'
                          }`}
                        >
                          {user.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderParentGroups = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading membership data...</span>
        </div>
      );
    }

    if (error) {
      return <div className="text-red-500 text-sm">{error}</div>;
    }

    if (!enhancedGroup?.ParentGroups || enhancedGroup.ParentGroups.length === 0) {
      return (
        <div className="text-gray-500 text-sm">
          Parent group information is not available in the current data.
          <br />
          <span className="text-xs">Note: This feature requires enhanced backend processing to identify which groups contain this group as a member.</span>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Building2 className="w-4 h-4" />
          <span>Member of {enhancedGroup.ParentGroups.length} groups</span>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
          {enhancedGroup.ParentGroups.map((parentGroup) => (
            <div
              key={parentGroup}
              className="px-2 py-1 bg-purple-50 text-purple-700 rounded text-sm cursor-pointer hover:bg-purple-100"
              onClick={() => onGroupClick?.(parentGroup)}
            >
              {parentGroup}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'direct'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('direct')}
          >
            Direct Members
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'resolved'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('resolved')}
          >
            All Users
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'parents'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('parents')}
          >
            Parent Groups
          </button>
        </div>
      </div>

      <div className="min-h-[200px]">
        {activeTab === 'direct' && renderDirectMembers()}
        {activeTab === 'resolved' && renderResolvedMembers()}
        {activeTab === 'parents' && renderParentGroups()}
      </div>
    </div>
  );
};

export default GroupMembershipInfo;
