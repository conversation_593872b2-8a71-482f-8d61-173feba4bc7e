import React, { useState } from 'react';
import { Users, User<PERSON>heck, Building2, ChevronDown, ChevronRight } from 'lucide-react';
import { Group, ResolvedUser } from './GroupTypes';

interface GroupMembershipInfoProps {
  group: Group;
  onGroupClick?: (groupName: string) => void;
}

const GroupMembershipInfo: React.FC<GroupMembershipInfoProps> = ({ 
  group, 
  onGroupClick 
}) => {
  const [activeTab, setActiveTab] = useState<'direct' | 'resolved' | 'parents'>('direct');
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());

  const togglePath = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const renderDirectMembers = () => {
    if (!group.DirectMembers) {
      return <div className="text-gray-500 text-sm">No enhanced membership data available</div>;
    }

    const { users, groups, total } = group.DirectMembers;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>Total: {total} members ({users.length} users, {groups.length} groups)</span>
        </div>

        {users.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <UserCheck className="w-4 h-4" />
              Direct Users ({users.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {users.map((user) => (
                <div key={user} className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-sm">
                  {user}
                </div>
              ))}
            </div>
          </div>
        )}

        {groups.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2 flex items-center gap-1">
              <Building2 className="w-4 h-4" />
              Direct Groups ({groups.length})
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
              {groups.map((groupName) => (
                <div 
                  key={groupName} 
                  className="px-2 py-1 bg-green-50 text-green-700 rounded text-sm cursor-pointer hover:bg-green-100"
                  onClick={() => onGroupClick?.(groupName)}
                >
                  {groupName}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderResolvedMembers = () => {
    if (!group.ResolvedMembers) {
      return <div className="text-gray-500 text-sm">No resolved membership data available</div>;
    }

    const { users, total } = group.ResolvedMembers;

    // Group users by their access path
    const usersByPath = users.reduce((acc, user) => {
      const pathKey = user.path.join(' → ');
      if (!acc[pathKey]) {
        acc[pathKey] = [];
      }
      acc[pathKey].push(user);
      return acc;
    }, {} as Record<string, ResolvedUser[]>);

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>Total: {total} users with access</span>
        </div>

        <div className="space-y-2">
          {Object.entries(usersByPath).map(([pathKey, pathUsers]) => {
            const isExpanded = expandedPaths.has(pathKey);
            const isDirect = pathUsers[0]?.direct;
            
            return (
              <div key={pathKey} className="border rounded-lg">
                <div 
                  className={`p-3 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                    isDirect ? 'bg-blue-50' : 'bg-gray-50'
                  }`}
                  onClick={() => togglePath(pathKey)}
                >
                  <div className="flex items-center gap-2">
                    {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                    <span className="text-sm font-medium">
                      {pathKey} {isDirect && <span className="text-blue-600">(Direct)</span>}
                    </span>
                    <span className="text-xs text-gray-500">({pathUsers.length} users)</span>
                  </div>
                </div>
                
                {isExpanded && (
                  <div className="p-3 border-t bg-white">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
                      {pathUsers.map((user) => (
                        <div 
                          key={user.name} 
                          className={`px-2 py-1 rounded text-sm ${
                            user.direct 
                              ? 'bg-blue-50 text-blue-700' 
                              : 'bg-gray-50 text-gray-700'
                          }`}
                        >
                          {user.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderParentGroups = () => {
    if (!group.ParentGroups || group.ParentGroups.length === 0) {
      return <div className="text-gray-500 text-sm">This group is not a member of any other groups</div>;
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Building2 className="w-4 h-4" />
          <span>Member of {group.ParentGroups.length} groups</span>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1">
          {group.ParentGroups.map((parentGroup) => (
            <div 
              key={parentGroup} 
              className="px-2 py-1 bg-purple-50 text-purple-700 rounded text-sm cursor-pointer hover:bg-purple-100"
              onClick={() => onGroupClick?.(parentGroup)}
            >
              {parentGroup}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'direct'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('direct')}
          >
            Direct Members
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'resolved'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('resolved')}
          >
            All Users
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'parents'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('parents')}
          >
            Parent Groups
          </button>
        </div>
      </div>

      <div className="min-h-[200px]">
        {activeTab === 'direct' && renderDirectMembers()}
        {activeTab === 'resolved' && renderResolvedMembers()}
        {activeTab === 'parents' && renderParentGroups()}
      </div>
    </div>
  );
};

export default GroupMembershipInfo;
