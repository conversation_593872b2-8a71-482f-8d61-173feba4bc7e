package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/patrickmn/go-cache"
)

// CacheKeys defines constants for cache keys
const (
	CacheKeyGroups      = "groups_%s"        // Format: groups_<repoPath>
	CacheKeyGroupsByLOB = "groups_lob_%s_%s" // Format: groups_lob_<repoPath>_<lob>
	CacheKeyPresets     = "presets"
	CacheKeyPreset      = "preset_%s" // Format: preset_<presetID>
	CacheKeyReports     = "reports"
	CacheKeyReport      = "report_%s"       // Format: report_<reportID>
	CacheKeyUsersForLOB = "users_lob_%s_%s" // Format: users_lob_<repoPath>_<lob>
)

// DataProcessor handles the processing of data from the repository
type DataProcessor struct {
	reportsFolder string
	dataFolder    string
	cache         *cache.Cache

	// Mutex to protect concurrent access to indices
	indicesMutex sync.RWMutex

	// Search indices for faster lookups
	nameIndex        map[string][]int // Maps names to indices in the groups slice
	lobIndex         map[string][]int // Maps LOBs to indices in the groups slice
	memberIndex      map[string][]int // Maps member names to group indices
	descriptionIndex map[string][]int // Maps words in descriptions to group indices

	// User indices
	userNameIndex  map[string][]int // Maps user names to indices in the users slice
	userGroupIndex map[string][]int // Maps group names to user indices
	userLobIndex   map[string][]int // Maps LOBs to user indices
}

// NewDataProcessor creates a new data processor instance
func NewDataProcessor(reportsFolder string) *DataProcessor {
	// Create a cache with a default expiration time of 30 minutes and purge expired items every 60 minutes
	c := cache.New(30*time.Minute, 60*time.Minute)

	// Ensure data directory exists
	dataDir := "data"
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		log.Printf("Warning: Failed to create data directory: %v", err)
	}

	return &DataProcessor{
		reportsFolder: reportsFolder,
		dataFolder:    dataDir,
		cache:         c,
		// Initialize empty indices
		nameIndex:        make(map[string][]int),
		lobIndex:         make(map[string][]int),
		memberIndex:      make(map[string][]int),
		descriptionIndex: make(map[string][]int),
		userNameIndex:    make(map[string][]int),
		userGroupIndex:   make(map[string][]int),
		userLobIndex:     make(map[string][]int),
	}
}

// ParseJSONFiles parses all JSON files in the directory and returns the groups
func (dp *DataProcessor) ParseJSONFiles(directoryPath string) ([]models.Group, error) {
	// Check cache first
	cacheKey := fmt.Sprintf(CacheKeyGroups, directoryPath)
	if cachedGroups, found := dp.cache.Get(cacheKey); found {
		log.Printf("Cache hit for %s", cacheKey)
		// Return a deep copy to prevent modification of cached data
		return copyGroups(cachedGroups.([]models.Group)), nil
	}
	log.Printf("Cache miss for %s, parsing JSON files", cacheKey)

	// Check if directory exists and is accessible before proceeding
	if _, err := os.Stat(directoryPath); err != nil {
		return nil, fmt.Errorf("error accessing directory: %w", err)
	}

	// Get list of all JSON files
	var jsonFiles []string
	err := filepath.Walk(directoryPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(info.Name(), ".json") {
			jsonFiles = append(jsonFiles, path)
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("error walking directory %s: %w", directoryPath, err)
	}

	log.Printf("Found %d JSON files in %s", len(jsonFiles), directoryPath)
	startTime := time.Now()

	// Use parallel processing to parse files
	var groups []models.Group
	var mutex sync.Mutex
	var wg sync.WaitGroup

	// Determine number of workers based on CPU cores
	numWorkers := runtime.NumCPU()
	log.Printf("Using %d workers for parallel JSON parsing", numWorkers)

	// Create a channel to distribute work
	filesChan := make(chan string, len(jsonFiles))

	// Start worker goroutines
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for path := range filesChan {
				// Process each file
				fileGroups, err := dp.parseJSONFile(path, directoryPath)
				if err != nil {
					log.Printf("Error parsing file %s: %v", path, err)
					continue
				}

				// Add results to the main groups slice
				if len(fileGroups) > 0 {
					mutex.Lock()
					groups = append(groups, fileGroups...)
					mutex.Unlock()
				}
			}
		}()
	}

	// Send files to workers
	for _, file := range jsonFiles {
		filesChan <- file
	}
	close(filesChan)

	// Wait for all workers to finish
	wg.Wait()

	elapsed := time.Since(startTime)
	log.Printf("Parsed %d JSON files in %v, found %d groups", len(jsonFiles), elapsed, len(groups))

	// Build search indices for faster lookups
	dp.buildGroupIndices(groups)

	// Cache the results
	dp.cache.Set(cacheKey, groups, cache.DefaultExpiration)

	return copyGroups(groups), nil
}

// parseJSONFile parses a single JSON file and returns the groups
func (dp *DataProcessor) parseJSONFile(path string, directoryPath string) ([]models.Group, error) {
	var fileGroups []models.Group

	// Read the file
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("error reading file %s: %w", path, err)
	}

	// Check if this is a group file by looking for a map structure with common group fields
	var rawMap map[string]interface{}
	if err := json.Unmarshal(data, &rawMap); err != nil {
		// Skip files that aren't valid JSON
		return nil, nil
	}

	// Check if it has the expected fields for a group
	_, hasGroupName := rawMap["Groupname"]
	_, hasOU := rawMap["OU"]

	// Skip non-group files that we know about
	if hasOU && !hasGroupName {
		// This is an OU file, not a group file
		return nil, nil
	}

	// Only process files that have a Groupname field
	if !hasGroupName {
		return nil, nil
	}

	// Get relative path for source file (from directoryPath)
	relPath, err := filepath.Rel(directoryPath, path)
	if err != nil {
		// Fall back to just the filename if we can't get relative path
		relPath = filepath.Base(path)
	}

	// Get LOB from parent folder name
	lob := dp.getParentFolderName(path)

	// Try to parse as a single group
	var singleGroup models.Group
	if err := json.Unmarshal(data, &singleGroup); err == nil && singleGroup.Groupname != "" {
		// Successfully parsed as a single group
		singleGroup.Lob = lob
		singleGroup.SourceFile = relPath
		return []models.Group{singleGroup}, nil
	}

	// Try to parse as an array of groups
	if err := json.Unmarshal(data, &fileGroups); err != nil {
		return nil, fmt.Errorf("error parsing JSON from file %s: %w", path, err)
	}

	// Set LOB and source file based on parent folder
	for i := range fileGroups {
		fileGroups[i].Lob = lob
		fileGroups[i].SourceFile = relPath
	}

	return fileGroups, nil
}

// ResolveMembers resolves any group references in the members field
func (dp *DataProcessor) ResolveMembers(groups []models.Group) {
	fmt.Printf("=== ResolveMembers: Processing %d groups with FIXED logic (no recursive resolution) ===\n", len(groups))

	// First, create a map of all group names for quick lookup
	groupNames := make(map[string]bool)
	for _, group := range groups {
		groupNames[strings.ToLower(strings.TrimSpace(group.Groupname))] = true
	}

	// Now process each group's members
	for i, group := range groups {
		var resolvedMembers models.GroupMembers
		for _, member := range group.Members {
			// Check if this member is a group (either by type or by name)
			isGroup := member.Type == models.GroupMemberType
			fmt.Printf("ResolveMembers: Processing member '%s' in group '%s', initial type: %s\n", member.Name, group.Groupname, member.Type)

			// If not already marked as a group, check if the name matches any group
			if !isGroup {
				foundGroup, isGroup := dp.getGroup(groups, member.Name)
				if isGroup {
					fmt.Printf("ResolveMembers: Found matching group '%s' for member '%s'\n", foundGroup.Groupname, member.Name)
				} else {
					fmt.Printf("ResolveMembers: No matching group found for member '%s'\n", member.Name)
				}
			}

			if isGroup {
				// This is a group reference, add it as a group
				// DO NOT recursively resolve nested group members here
				// The frontend will handle the display logic for nested groups
				fmt.Printf("ResolveMembers: Marking '%s' as GROUP in group '%s'\n", member.Name, group.Groupname)
				resolvedMembers = append(resolvedMembers, models.Member{
					Name: member.Name,
					Type: models.GroupMemberType,
				})
			} else {
				// This is a regular user
				fmt.Printf("ResolveMembers: Marking '%s' as USER in group '%s'\n", member.Name, group.Groupname)
				resolvedMembers = append(resolvedMembers, models.Member{
					Name: member.Name,
					Type: models.UserMemberType,
				})
			}
		}
		groups[i].Members = resolvedMembers
	}
}

// Note: The resolveGroupMembersWithType method was removed as it was causing issues
// with member type classification. The recursive resolution was adding individual users
// from nested groups to parent groups, causing all members to appear as direct members
// instead of maintaining proper group hierarchy.

// getGroup finds a group by name
func (dp *DataProcessor) getGroup(groups []models.Group, groupname string) (models.Group, bool) {
	// Normalize the input groupname for comparison
	normalizedName := strings.ToLower(strings.TrimSpace(groupname))

	for _, group := range groups {
		// Normalize the group name for comparison
		normalizedGroupName := strings.ToLower(strings.TrimSpace(group.Groupname))

		if normalizedGroupName == normalizedName {
			return group, true
		}
	}
	return models.Group{}, false
}

// Note: The GetGroupsByLOB method was removed as it was redundant with the filtering
// functionality now handled directly in the controller methods using QueryParams.

// FindGroupsForMember returns a list of group names that the member belongs to
func (dp *DataProcessor) FindGroupsForMember(groups []models.Group, member string) []string {
	var memberGroups []string
	// Always use case-insensitive matching
	memberLower := strings.ToLower(member)

	for _, group := range groups {
		for _, groupMember := range group.Members {
			// Use case-insensitive comparison
			if strings.ToLower(groupMember.Name) == memberLower {
				memberGroups = append(memberGroups, group.Groupname)
				break
			}
		}
	}

	return memberGroups
}

// GenerateGroupReport exports groups filtered by LOB to a JSON file
func (dp *DataProcessor) GenerateGroupReport(groups []models.Group, lob, filename string) error {
	if err := os.MkdirAll(dp.reportsFolder, 0755); err != nil {
		return fmt.Errorf("error creating reports directory: %w", err)
	}

	outputPath := filepath.Join(dp.reportsFolder, filename)

	// Filter groups by LOB
	var filteredGroups []models.Group
	for _, group := range groups {
		if group.Lob == lob {
			filteredGroups = append(filteredGroups, group)
		}
	}

	// Marshal to JSON
	data, err := json.MarshalIndent(filteredGroups, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputPath, data, 0644); err != nil {
		return fmt.Errorf("error writing file: %w", err)
	}

	return nil
}

// GenerateUserReport generates a report of users with their group memberships
func (dp *DataProcessor) GenerateUserReport(groups []models.Group, lob, filename string) error {
	if err := os.MkdirAll(dp.reportsFolder, 0755); err != nil {
		return fmt.Errorf("error creating reports directory: %w", err)
	}

	outputPath := filepath.Join(dp.reportsFolder, filename)

	// Process all members
	processedMembers := make(map[string]bool)
	var users []models.User

	// If LOB is specified, filter by LOB
	var targetGroups []models.Group
	if lob != "" {
		for _, group := range groups {
			if hasLob(group, lob) {
				targetGroups = append(targetGroups, group)
			}
		}
	} else {
		targetGroups = groups
	}

	// Get unique users from all groups
	for _, group := range targetGroups {
		for _, member := range group.Members {
			if !processedMembers[member.Name] {
				memberGroups := dp.FindGroupsForMember(groups, member.Name)
				if len(memberGroups) > 0 {
					users = append(users, models.User{
						Name:   member.Name,
						Groups: memberGroups,
					})
				}
				processedMembers[member.Name] = true
			}
		}
	}

	// Marshal to JSON
	data, err := json.MarshalIndent(users, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputPath, data, 0644); err != nil {
		return fmt.Errorf("error writing file: %w", err)
	}

	return nil
}

// Note: The userHasLob function was removed as it was not used anywhere in the codebase.
// LOB filtering is now handled through the QueryParams structure.

// GenerateReportFromPreset generates a report based on a preset
func (dp *DataProcessor) GenerateReportFromPreset(groups []models.Group, presetID string) (models.Report, error) {
	log.Printf("GenerateReportFromPreset called for preset ID: %s with %d groups", presetID, len(groups))

	// Get the preset
	preset, err := dp.GetPreset(presetID)
	if err != nil {
		log.Printf("Error getting preset %s: %v", presetID, err)
		return models.Report{}, fmt.Errorf("error getting preset: %w", err)
	}
	log.Printf("Found preset %s: %s (type: %s, active: %t)", preset.ID, preset.Name, preset.ReportType, preset.IsActive)

	// Check if preset is active
	if !preset.IsActive {
		log.Printf("Cannot generate report from inactive preset %s", presetID)
		return models.Report{}, fmt.Errorf("cannot generate report from inactive preset")
	}

	// Generate the report with preset version
	log.Printf("Calling GenerateReport for preset %s with %d groups", presetID, len(groups))
	report, err := dp.GenerateReport(groups, preset.ReportType, preset.Query, preset.ID, preset.Name)
	if err != nil {
		log.Printf("Error generating report for preset %s: %v", presetID, err)
		return models.Report{}, err
	}
	log.Printf("Successfully generated report %s for preset %s", report.ID, presetID)

	// Add preset version and shared ID to the report
	report.PresetVersion = preset.Version
	report.SharedPresetID = preset.SharedID
	log.Printf("Added preset version %d and shared ID %s to report %s", preset.Version, preset.SharedID, report.ID)

	// Note: We don't save the report metadata here
	// The controller will set the repositoryId and save it

	return report, nil
}

// GetCurrentDate returns the current date in YYYYMMDD format
func GetCurrentDate() string {
	return time.Now().Format("20060102")
}

// GetReportsFolder returns the reports folder path
func (dp *DataProcessor) GetReportsFolder() string {
	return dp.reportsFolder
}

// getParentFolderName extracts the parent folder name from a path
// For files in a subfolder (e.g., repos/repo_1/fm/file.json), the LOB is the subfolder name ("fm")
// For files at the repo root (e.g., repos/repo_1/file.json), the LOB should be empty
func (dp *DataProcessor) getParentFolderName(path string) string {
	// Get directory containing the file
	dirPath := filepath.Dir(path)

	// If the file is directly under the repo root, return empty string for LOB
	if dp.isRepoRootFolder(dirPath) {
		return "" // Empty LOB for files directly in the repo root
	}

	// Otherwise, get the parent folder name (immediate parent of the file)
	folderName := filepath.Base(dirPath)
	return folderName
}

// isRepoRootFolder checks if a given path is the root of a repository
func (dp *DataProcessor) isRepoRootFolder(dirPath string) bool {
	// Split the path into components
	components := strings.Split(filepath.ToSlash(dirPath), "/")

	// If the path looks like: .../repos/<repo-id>, it's the root
	// The path should have at least 2 components
	if len(components) >= 2 {
		// Check if the parent directory is "repos"
		parentDir := components[len(components)-2]
		return parentDir == "repos"
	}

	return false
}

// Preset and Report management functions
const (
	presetsFilename  = "report_presets.json"
	reportsFilename  = "reports.json"
	usersReportType  = "users"
	groupsReportType = "groups"
	bothReportType   = "both"
)

// SavePreset saves a report preset
// If updating an existing preset, it creates a new version
func (dp *DataProcessor) SavePreset(preset models.ReportPreset) (models.ReportPreset, error) {
	// Ensure data directory exists
	if err := os.MkdirAll(dp.dataFolder, 0755); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error creating data directory: %w", err)
	}

	// Validate reportType
	switch preset.ReportType {
	case usersReportType, groupsReportType, bothReportType:
		// Valid report type
	default:
		return models.ReportPreset{}, fmt.Errorf("invalid report type: %s. Must be one of: users, groups, both", preset.ReportType)
	}

	presetFilePath := filepath.Join(dp.dataFolder, presetsFilename)

	// Read existing presets
	presets, err := dp.GetPresets()
	if err != nil && !os.IsNotExist(err) {
		return models.ReportPreset{}, fmt.Errorf("error reading presets: %w", err)
	}

	now := time.Now().Format(time.RFC3339)

	// Check if this is a new preset or an update to an existing one
	if preset.ID == "" {
		// New preset
		preset.ID = fmt.Sprintf("preset_%d", time.Now().UnixNano())
		preset.CreatedAt = now
		preset.UpdatedAt = now
		preset.Version = 1
		preset.IsActive = true
		preset.ParentID = preset.ID // Parent ID is the same as ID for the first version

		// Generate a shared ID for all versions of this preset
		// Use a format that's clearly different from the regular ID
		preset.SharedID = fmt.Sprintf("shared_%d", time.Now().UnixNano())

		// Add to presets list
		presets = append(presets, preset)
	} else {
		// Update to existing preset - create a new version
		var existingPreset models.ReportPreset
		var found bool

		// Find the existing preset
		for _, p := range presets {
			if p.ID == preset.ID {
				existingPreset = p
				found = true
				break
			}
		}

		if !found {
			return models.ReportPreset{}, fmt.Errorf("preset not found")
		}

		// Create a new version with a new ID but keep track of the original
		parentID := preset.ID
		if existingPreset.ParentID != "" {
			// If this is already a version of another preset, use the original parent
			parentID = existingPreset.ParentID
		}

		// Get the highest version number for this preset family
		highestVersion := 0
		for _, p := range presets {
			if p.ParentID == parentID && p.Version > highestVersion {
				highestVersion = p.Version
			}
		}

		// Create new version
		preset.ID = fmt.Sprintf("preset_%d", time.Now().UnixNano())
		preset.CreatedAt = now
		preset.UpdatedAt = now
		preset.Version = highestVersion + 1
		preset.IsActive = true
		preset.ParentID = parentID

		// Use the same shared ID as the existing preset
		if existingPreset.SharedID != "" {
			preset.SharedID = existingPreset.SharedID
		} else {
			// If the existing preset doesn't have a shared ID, create one
			preset.SharedID = fmt.Sprintf("shared_%d", time.Now().UnixNano())

			// Also update the existing preset with this shared ID
			for i, p := range presets {
				if p.ID == existingPreset.ID || p.ParentID == parentID || p.ID == parentID {
					presets[i].SharedID = preset.SharedID
				}
			}
		}

		// Add to presets list
		presets = append(presets, preset)
	}

	// Write updated presets back to file
	data, err := json.MarshalIndent(presets, "", "    ")
	if err != nil {
		return models.ReportPreset{}, fmt.Errorf("error marshaling presets: %w", err)
	}

	if err := os.WriteFile(presetFilePath, data, 0644); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error writing presets: %w", err)
	}

	// Invalidate cache
	dp.cache.Delete(CacheKeyPresets)

	return preset, nil
}

// UpdatePresetInPlace updates a preset without creating a new version
// This is used by the scheduler to update the nextRun field without creating a new version
func (dp *DataProcessor) UpdatePresetInPlace(preset models.ReportPreset) (models.ReportPreset, error) {
	// Ensure data directory exists
	if err := os.MkdirAll(dp.dataFolder, 0755); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error creating data directory: %w", err)
	}

	presetFilePath := filepath.Join(dp.dataFolder, presetsFilename)

	// Read existing presets
	presets, err := dp.GetPresets()
	if err != nil && !os.IsNotExist(err) {
		return models.ReportPreset{}, fmt.Errorf("error reading presets: %w", err)
	}

	// Find the preset to update
	var found bool
	for i, p := range presets {
		if p.ID == preset.ID {
			// Update the preset in place
			preset.UpdatedAt = time.Now().Format(time.RFC3339)
			presets[i] = preset
			found = true
			break
		}
	}

	if !found {
		return models.ReportPreset{}, fmt.Errorf("preset not found")
	}

	// Write updated presets back to file
	data, err := json.MarshalIndent(presets, "", "    ")
	if err != nil {
		return models.ReportPreset{}, fmt.Errorf("error marshaling presets: %w", err)
	}

	if err := os.WriteFile(presetFilePath, data, 0644); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error writing presets: %w", err)
	}

	// Invalidate cache
	dp.cache.Delete(CacheKeyPresets)
	dp.cache.Delete(fmt.Sprintf(CacheKeyPreset, preset.ID))

	return preset, nil
}

// GetPresets retrieves all saved report presets
func (dp *DataProcessor) GetPresets() ([]models.ReportPreset, error) {
	// Always read from file to ensure we have the latest data
	presetFilePath := filepath.Join(dp.dataFolder, presetsFilename)

	// Check if file exists
	if _, err := os.Stat(presetFilePath); os.IsNotExist(err) {
		// Try the old location for backward compatibility
		oldPath := filepath.Join(dp.reportsFolder, presetsFilename)
		if _, err := os.Stat(oldPath); os.IsNotExist(err) {
			return []models.ReportPreset{}, nil
		}

		// Read from old location
		data, err := os.ReadFile(oldPath)
		if err != nil {
			return nil, fmt.Errorf("error reading presets file from old location: %w", err)
		}

		// Migrate to new location
		if err := os.MkdirAll(dp.dataFolder, 0755); err != nil {
			return nil, fmt.Errorf("error creating data directory: %w", err)
		}

		if err := os.WriteFile(presetFilePath, data, 0644); err != nil {
			return nil, fmt.Errorf("error migrating presets file to new location: %w", err)
		}

		return dp.GetPresets()
	}

	// Read file
	data, err := os.ReadFile(presetFilePath)
	if err != nil {
		return nil, fmt.Errorf("error reading presets file: %w", err)
	}

	// Unmarshal JSON
	var presets []models.ReportPreset
	if err := json.Unmarshal(data, &presets); err != nil {
		return nil, fmt.Errorf("error parsing presets file: %w", err)
	}

	// Filter out presets without IDs
	var validPresets []models.ReportPreset
	var invalidCount int
	for _, preset := range presets {
		if preset.ID == "" {
			invalidCount++
			continue
		}
		validPresets = append(validPresets, preset)
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found %d presets with missing IDs that were filtered out", invalidCount)
	}

	// Handle migration of existing presets to include new fields
	presetsUpdated := false

	// First pass: Set basic fields and collect parent IDs
	parentIDMap := make(map[string][]int) // Map of parent IDs to preset indices
	for i := range presets {
		// Set default values for new fields if they're not set
		if presets[i].Version == 0 {
			presets[i].Version = 1
			presetsUpdated = true
		}

		// Set ParentID to the preset's own ID if not set (for first version)
		if presets[i].ParentID == "" {
			presets[i].ParentID = presets[i].ID
			presetsUpdated = true
		}

		// Collect presets by parent ID for shared ID assignment
		parentIDMap[presets[i].ParentID] = append(parentIDMap[presets[i].ParentID], i)
	}

	// Second pass: Assign shared IDs to preset families
	for parentID, indices := range parentIDMap {
		// Skip if all presets in this family already have the same shared ID
		allHaveSameSharedID := true
		var existingSharedID string

		if len(indices) > 0 && presets[indices[0]].SharedID != "" {
			existingSharedID = presets[indices[0]].SharedID
			for _, idx := range indices {
				if presets[idx].SharedID != existingSharedID {
					allHaveSameSharedID = false
					break
				}
			}
		} else {
			allHaveSameSharedID = false
		}

		if !allHaveSameSharedID {
			// Generate a new shared ID for this family
			sharedID := fmt.Sprintf("shared_%s", parentID)

			// Assign to all presets in this family
			for _, idx := range indices {
				presets[idx].SharedID = sharedID
				presetsUpdated = true
			}
		}
	}

	// If we updated any presets, save the changes back to the file
	if presetsUpdated {
		// Use the validPresets for saving back to file to ensure we don't save invalid presets
		updatedData, err := json.MarshalIndent(validPresets, "", "    ")
		if err != nil {
			fmt.Printf("Warning: Failed to marshal updated presets: %v\n", err)
		} else {
			if err := os.WriteFile(presetFilePath, updatedData, 0644); err != nil {
				fmt.Printf("Warning: Failed to write updated presets: %v\n", err)
			}
		}
	}

	// Don't cache the results to ensure we always get fresh data
	log.Printf("Returning %d valid presets (filtered out %d invalid presets)", len(validPresets), invalidCount)
	return validPresets, nil
}

// GetPreset retrieves a specific preset by ID
func (dp *DataProcessor) GetPreset(presetID string) (models.ReportPreset, error) {
	// Always read from file to ensure we have the latest data
	presets, err := dp.GetPresets()
	if err != nil {
		return models.ReportPreset{}, err
	}

	for _, preset := range presets {
		if preset.ID == presetID {
			return preset, nil
		}
	}

	return models.ReportPreset{}, fmt.Errorf("preset not found")
}

// GetPresetVersions retrieves all versions of a preset by its parent ID
func (dp *DataProcessor) GetPresetVersions(parentID string) ([]models.ReportPreset, error) {
	presets, err := dp.GetPresets()
	if err != nil {
		return nil, err
	}

	// First, check if the provided ID is a parent ID
	var versions []models.ReportPreset

	// Find all presets with the matching parent ID
	for _, preset := range presets {
		if preset.ParentID == parentID {
			versions = append(versions, preset)
		}
	}

	// If no versions found, check if the ID itself is a preset ID and get its family
	if len(versions) == 0 {
		// Find the preset with this ID to get its parent ID
		var foundPreset models.ReportPreset
		var found bool

		for _, preset := range presets {
			if preset.ID == parentID {
				foundPreset = preset
				found = true
				break
			}
		}

		if !found {
			return nil, fmt.Errorf("preset not found")
		}

		// Use the parent ID to find all versions
		actualParentID := foundPreset.ParentID
		for _, preset := range presets {
			if preset.ParentID == actualParentID {
				versions = append(versions, preset)
			}
		}
	}

	// Sort versions by version number
	sort.Slice(versions, func(i, j int) bool {
		return versions[i].Version < versions[j].Version
	})

	return versions, nil
}

// TogglePresetActivation toggles the activation status of a report preset
func (dp *DataProcessor) TogglePresetActivation(presetID string) (models.ReportPreset, error) {
	// Completely clear all caches
	dp.cache.Flush()

	// Read presets directly from file
	presetFilePath := filepath.Join(dp.dataFolder, presetsFilename)

	// Read the file
	fileData, err := os.ReadFile(presetFilePath)
	if err != nil {
		return models.ReportPreset{}, fmt.Errorf("error reading presets file: %w", err)
	}

	// Unmarshal into a slice of structs
	var presets []models.ReportPreset
	if err := json.Unmarshal(fileData, &presets); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error parsing presets file: %w", err)
	}

	// Find the preset
	var presetIndex int = -1
	found := false

	for i, preset := range presets {
		if preset.ID == presetID {
			presetIndex = i
			found = true
			break
		}
	}

	if !found {
		return models.ReportPreset{}, fmt.Errorf("preset not found")
	}

	// Toggle the activation status
	currentState := presets[presetIndex].IsActive
	presets[presetIndex].IsActive = !currentState
	presets[presetIndex].UpdatedAt = time.Now().Format(time.RFC3339)

	// Log the change for debugging
	fmt.Printf("Toggling preset activation for %s: %v -> %v\n",
		presetID, currentState, presets[presetIndex].IsActive)

	// Create a copy of the updated preset to return
	updatedPreset := presets[presetIndex]

	// Write updated presets back to file
	data, err := json.MarshalIndent(presets, "", "    ")
	if err != nil {
		return models.ReportPreset{}, fmt.Errorf("error marshaling presets: %w", err)
	}

	// Write directly to the file
	if err := os.WriteFile(presetFilePath, data, 0644); err != nil {
		return models.ReportPreset{}, fmt.Errorf("error writing presets file: %w", err)
	}

	// Force sync to disk
	file, err := os.OpenFile(presetFilePath, os.O_RDWR, 0644)
	if err == nil {
		file.Sync()
		file.Close()
	}

	// Verify the change was made by reading directly from the file again
	verifyData, err := os.ReadFile(presetFilePath)
	if err != nil {
		fmt.Printf("WARNING: Could not verify preset change: %v\n", err)
	} else {
		fmt.Printf("DEBUG: File content after toggle: %s\n", string(verifyData))

		// Check the structured data
		var verifyPresets []models.ReportPreset
		if err := json.Unmarshal(verifyData, &verifyPresets); err != nil {
			fmt.Printf("WARNING: Could not parse presets file for verification: %v\n", err)
		} else {
			var found bool
			for _, p := range verifyPresets {
				if p.ID == presetID {
					found = true
					if p.IsActive == currentState {
						fmt.Printf("WARNING: Preset activation toggle failed for %s. Still in state: %v\n",
							presetID, p.IsActive)
					} else {
						fmt.Printf("Successfully toggled preset activation for %s to %v\n",
							presetID, p.IsActive)
					}
					break
				}
			}

			if !found {
				fmt.Printf("WARNING: Could not find preset %s after toggle operation\n", presetID)
			}
		}
	}

	// Completely clear all caches again
	dp.cache.Flush()

	return updatedPreset, nil
}

// HasReportsForPreset checks if there are any reports associated with a preset
func (dp *DataProcessor) HasReportsForPreset(presetID string) (bool, error) {
	reports, err := dp.GetReports()
	if err != nil {
		return false, err
	}

	for _, report := range reports {
		if report.PresetID == presetID {
			return true, nil
		}
	}

	return false, nil
}

// DeletePreset deletes a report preset if it has no reports, otherwise just deactivates it
func (dp *DataProcessor) DeletePreset(presetID string, schedulerService interface{}) error {
	// Check if there are any reports for this preset
	hasReports, err := dp.HasReportsForPreset(presetID)
	if err != nil {
		return fmt.Errorf("error checking for reports: %w", err)
	}

	if hasReports {
		// If there are reports, just deactivate the preset
		_, err := dp.TogglePresetActivation(presetID)
		return err
	}

	// If there are no reports, actually delete the preset
	presets, err := dp.GetPresets()
	if err != nil {
		return fmt.Errorf("error getting presets: %w", err)
	}

	// Find the preset to delete and all its versions
	var updatedPresets []models.ReportPreset
	var found bool
	var parentID string
	var presetIDs []string // Track all preset IDs to delete execution history for

	// First, find the preset and get its parent ID
	for _, preset := range presets {
		if preset.ID == presetID {
			found = true
			parentID = preset.ParentID
			break
		}
	}

	if !found {
		return fmt.Errorf("preset not found")
	}

	// Filter out the preset and all its versions
	for _, preset := range presets {
		if preset.ID == presetID || preset.ParentID == parentID {
			// This is a preset we're deleting, add to the list of IDs
			presetIDs = append(presetIDs, preset.ID)
		} else {
			updatedPresets = append(updatedPresets, preset)
		}
	}

	// Write updated presets back to file
	presetFilePath := filepath.Join(dp.dataFolder, presetsFilename)
	data, err := json.MarshalIndent(updatedPresets, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling presets: %w", err)
	}

	if err := os.WriteFile(presetFilePath, data, 0644); err != nil {
		return fmt.Errorf("error writing presets: %w", err)
	}

	// Invalidate cache
	dp.cache.Delete(CacheKeyPresets)
	dp.cache.Delete(fmt.Sprintf(CacheKeyPreset, presetID))

	// Delete execution history files for all deleted presets
	if schedulerService != nil {
		if scheduler, ok := schedulerService.(interface{ DeleteExecutionsForPreset(string) error }); ok {
			for _, id := range presetIDs {
				if err := scheduler.DeleteExecutionsForPreset(id); err != nil {
					log.Printf("Warning: Failed to delete execution history for preset %s: %v", id, err)
				} else {
					log.Printf("Successfully deleted execution history for preset %s", id)
				}
			}
		}
	}

	return nil
}

// GenerateReport creates a report based on a preset or query parameters
func (dp *DataProcessor) GenerateReport(groups []models.Group, reportType string, query models.QueryParams, presetID, presetName string) (models.Report, error) {
	// Ensure reports directory exists
	if err := os.MkdirAll(dp.reportsFolder, 0755); err != nil {
		return models.Report{}, fmt.Errorf("error creating reports directory: %w", err)
	}

	now := time.Now()
	nowStr := now.Format(time.RFC3339)
	dateStr := now.Format("20060102_150405")

	// Generate unique filename based on report type and timestamp
	var filename string
	if presetName != "" {
		filename = fmt.Sprintf("%s_%s.json", strings.ReplaceAll(presetName, " ", "_"), dateStr)
	} else {
		switch reportType {
		case usersReportType:
			filename = fmt.Sprintf("users_%s.json", dateStr)
		case groupsReportType:
			filename = fmt.Sprintf("groups_%s.json", dateStr)
		case bothReportType:
			filename = fmt.Sprintf("users_and_groups_%s.json", dateStr)
		default:
			return models.Report{}, fmt.Errorf("invalid report type: %s", reportType)
		}
	}

	// Add lob prefix if specified
	if query.LOB != "" {
		filename = fmt.Sprintf("%s_%s", query.LOB, filename)
	}

	// Generate the actual report data
	var data []byte
	var err error

	if reportType == groupsReportType || reportType == bothReportType {
		// Filter and export groups
		filteredGroups := dp.FilterGroups(groups, query)
		if reportType == groupsReportType {
			data, err = json.MarshalIndent(filteredGroups, "", "    ")
		} else {
			// Both users and groups
			users := dp.ExtractUsers(groups, query)
			data, err = json.MarshalIndent(map[string]interface{}{
				"groups": filteredGroups,
				"users":  users,
			}, "", "    ")
		}
	} else {
		// Export only users
		users := dp.ExtractUsers(groups, query)
		data, err = json.MarshalIndent(users, "", "    ")
	}

	if err != nil {
		return models.Report{}, fmt.Errorf("error marshaling report data: %w", err)
	}

	// Write to file
	reportPath := filepath.Join(dp.reportsFolder, filename)
	if err := os.WriteFile(reportPath, data, 0644); err != nil {
		return models.Report{}, fmt.Errorf("error writing report: %w", err)
	}

	// Create file info for size
	fileInfo, err := os.Stat(reportPath)
	if err != nil {
		// Don't fail, just set size to 0
		fileInfo = nil
	}

	// Create the report metadata but don't save it yet
	// The controller will set the repositoryId and save it
	report := models.Report{
		ID:          fmt.Sprintf("report_%d", now.UnixNano()),
		PresetID:    presetID,
		PresetName:  presetName,
		Filename:    filename,
		Type:        reportType,
		Size:        fileInfo.Size(),
		CreatedAt:   nowStr,
		DownloadURL: fmt.Sprintf("/api/data/reports/download/%s", filename),
		// Add metadata about flattening
		Metadata: map[string]interface{}{
			"flattenMembership": query.FlattenMembership,
		},
	}

	// If this is a report from a preset, try to get the preset to add its shared ID
	if presetID != "" {
		preset, err := dp.GetPreset(presetID)
		if err == nil && preset.SharedID != "" {
			report.SharedPresetID = preset.SharedID
			report.PresetVersion = preset.Version
		}
	}

	// Invalidate any cached users for this query
	dp.invalidateUserCache(query)

	return report, nil
}

// FilterGroups applies the query parameters to filter groups
// This method is used by the GenerateReport method and in tests
func (dp *DataProcessor) FilterGroups(groups []models.Group, query models.QueryParams) []models.Group {
	var filtered []models.Group

	for _, group := range groups {
		// Filter by LOB if specified
		if query.LOB != "" && group.Lob != query.LOB {
			continue
		}

		// Filter by group type if specified
		if len(query.Types) > 0 {
			typeMatch := false
			for _, t := range query.Types {
				if t == group.Type {
					typeMatch = true
					break
				}
			}
			if !typeMatch {
				continue
			}
		}

		// Filter by group IDs if specified
		if len(query.GroupIDs) > 0 {
			idMatch := false
			for _, id := range query.GroupIDs {
				if id == group.Groupname {
					idMatch = true
					break
				}
			}
			if !idMatch {
				continue
			}
		}

		filtered = append(filtered, group)
	}

	return filtered
}

// ExtractUsers creates a list of users that match the query parameters
func (dp *DataProcessor) ExtractUsers(groups []models.Group, query models.QueryParams) []models.User {
	// Check cache first
	cacheKey := fmt.Sprintf("users_query_%s_%s", getGroupsKey(groups), query.GetCacheKey())
	if cachedUsers, found := dp.cache.Get(cacheKey); found {
		return cachedUsers.([]models.User)
	}

	// Create a map to avoid duplicates - use lowercase keys for case-insensitive matching
	usersMap := make(map[string]models.User)

	// Create a map of groups by name for quick lookup
	groupsByName := make(map[string]models.Group)
	for _, g := range groups {
		groupsByName[g.Groupname] = g
	}

	// Create a set of group names for quick lookup
	groupNames := make(map[string]bool)
	for _, g := range groups {
		groupNames[strings.ToLower(g.Groupname)] = true
	}

	// Filter groups by LOB if specified
	var filteredGroups []models.Group
	if query.LOB != "" {
		for _, group := range groups {
			if hasLob(group, query.LOB) {
				filteredGroups = append(filteredGroups, group)
			}
		}
	} else {
		filteredGroups = groups
	}

	// Process each group
	for _, group := range filteredGroups {
		// If flattening is enabled, we'll extract all users from nested groups
		if query.FlattenMembership {
			// Extract all users from this group, including nested groups
			dp.extractUsersFromGroupRecursively(group, groups, usersMap, groupNames, make(map[string]bool))
		} else {
			// Standard processing without flattening
			for _, member := range group.Members {
				// Skip group references
				if member.Type == models.GroupMemberType {
					continue
				}

				// Skip if the member name is a known group name
				if groupNames[strings.ToLower(member.Name)] {
					continue
				}

				// Add user to the map
				dp.addUserToMap(member.Name, group, usersMap)
			}
		}
	}

	// Convert map to slice
	users := make([]models.User, 0, len(usersMap))
	for _, user := range usersMap {
		// Apply UserIDs filter if specified
		if len(query.UserIDs) > 0 {
			found := false
			for _, id := range query.UserIDs {
				if id == user.Name {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		users = append(users, user)
	}

	// Sort users by name
	sort.Slice(users, func(i, j int) bool {
		return users[i].Name < users[j].Name
	})

	// Build search indices for users
	dp.buildUserIndices(users)

	// Cache the results
	dp.cache.Set(cacheKey, users, cache.DefaultExpiration)

	return users
}

// extractUsersFromGroupRecursively extracts all users from a group, including users from nested groups
// When flattening is enabled, it only includes users, not groups, in the final result
func (dp *DataProcessor) extractUsersFromGroupRecursively(group models.Group, allGroups []models.Group, usersMap map[string]models.User, groupNames map[string]bool, visitedGroups map[string]bool) {
	// Avoid infinite recursion by tracking visited groups
	if visitedGroups[group.Groupname] {
		return
	}
	visitedGroups[group.Groupname] = true

	// Process each member of the group
	for _, member := range group.Members {
		if member.Type == models.GroupMemberType {
			// This is a group reference, recursively process it
			referencedGroup, exists := dp.getGroup(allGroups, member.Name)
			if exists {
				dp.extractUsersFromGroupRecursively(referencedGroup, allGroups, usersMap, groupNames, visitedGroups)
			}
		} else {
			// This is a user, add it to the map if it's not a group name
			if !groupNames[strings.ToLower(member.Name)] {
				dp.addUserToMap(member.Name, group, usersMap)
			}
		}
	}
}

// addUserToMap adds or updates a user in the users map
func (dp *DataProcessor) addUserToMap(memberName string, group models.Group, usersMap map[string]models.User) {
	// Use lowercase member name as map key for case-insensitive matching
	memberNameKey := strings.ToLower(memberName)

	// Create or update user entry
	user, exists := usersMap[memberNameKey]
	if !exists {
		user = models.User{
			Name:   memberName,
			Groups: []string{group.Groupname},
			LOBs:   []string{group.Lob},
			GroupDetails: []models.GroupDetail{
				{
					Name:       group.Groupname,
					LOB:        group.Lob,
					SourceFile: group.SourceFile,
				},
			},
		}
	} else {
		// Check if this group is already recorded for the user
		groupExists := false
		for _, g := range user.Groups {
			if g == group.Groupname {
				groupExists = true
				break
			}
		}
		if !groupExists {
			user.Groups = append(user.Groups, group.Groupname)

			// Add group detail
			user.GroupDetails = append(user.GroupDetails, models.GroupDetail{
				Name:       group.Groupname,
				LOB:        group.Lob,
				SourceFile: group.SourceFile,
			})

			// Check if this LOB is already in the user's LOBs
			lobExists := false
			for _, l := range user.LOBs {
				if l == group.Lob {
					lobExists = true
					break
				}
			}
			if !lobExists {
				user.LOBs = append(user.LOBs, group.Lob)
			}
		}
	}
	usersMap[memberNameKey] = user
}

// saveReportMetadata saves report metadata to the reports registry
func (dp *DataProcessor) saveReportMetadata(report models.Report) error {
	// Ensure data directory exists
	if err := os.MkdirAll(dp.dataFolder, 0755); err != nil {
		return fmt.Errorf("error creating data directory: %w", err)
	}

	reportsFilePath := filepath.Join(dp.dataFolder, reportsFilename)

	// Check if file exists in old location for migration
	oldPath := filepath.Join(dp.reportsFolder, reportsFilename)
	if _, err := os.Stat(reportsFilePath); os.IsNotExist(err) {
		if _, err := os.Stat(oldPath); err == nil {
			// Migrate from old location
			if data, err := os.ReadFile(oldPath); err == nil {
				if err := os.WriteFile(reportsFilePath, data, 0644); err != nil {
					return fmt.Errorf("error migrating reports file: %w", err)
				}
			}
		}
	}

	// Read existing reports
	var reports []models.Report
	if data, err := os.ReadFile(reportsFilePath); err == nil {
		if err := json.Unmarshal(data, &reports); err != nil {
			// If we can't parse, start fresh
			reports = []models.Report{}
		}
	}

	// If this is a report from a preset but missing the shared ID, try to get it from the preset
	if report.PresetID != "" && report.SharedPresetID == "" {
		preset, err := dp.GetPreset(report.PresetID)
		if err == nil && preset.SharedID != "" {
			log.Printf("Adding missing shared preset ID %s to report %s from preset %s",
				preset.SharedID, report.ID, report.PresetID)
			report.SharedPresetID = preset.SharedID

			// Also set the preset version if not already set
			if report.PresetVersion == 0 {
				report.PresetVersion = preset.Version
			}
		}
	}

	// Check if we're updating an existing report
	updated := false
	for i, existingReport := range reports {
		if existingReport.ID == report.ID {
			// Update existing report
			reports[i] = report
			updated = true
			log.Printf("Updating existing report %s with shared preset ID %s",
				report.ID, report.SharedPresetID)
			break
		}
	}

	// If not updating, add new report
	if !updated {
		reports = append(reports, report)
		log.Printf("Adding new report %s with shared preset ID %s",
			report.ID, report.SharedPresetID)
	}

	// Write back to file
	data, err := json.MarshalIndent(reports, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling reports: %w", err)
	}

	// Invalidate cache
	dp.cache.Delete(CacheKeyReports)
	dp.cache.Delete(fmt.Sprintf(CacheKeyReport, report.ID))

	return os.WriteFile(reportsFilePath, data, 0644)
}

// SaveReportMetadata is a public wrapper for saveReportMetadata
func (dp *DataProcessor) SaveReportMetadata(report models.Report) error {
	return dp.saveReportMetadata(report)
}

// GetReports retrieves all saved report metadata
func (dp *DataProcessor) GetReports() ([]models.Report, error) {
	// Temporarily disable cache to ensure we always get the latest data
	// This is to fix the issue with shared preset IDs not showing up in reports
	// Comment out the cache check for now
	/*
		if cachedReports, found := dp.cache.Get(CacheKeyReports); found {
			return cachedReports.([]models.Report), nil
		}
	*/

	reportsFilePath := filepath.Join(dp.dataFolder, reportsFilename)

	// Check if file exists
	if _, err := os.Stat(reportsFilePath); os.IsNotExist(err) {
		// Try the old location for backward compatibility
		oldPath := filepath.Join(dp.reportsFolder, reportsFilename)
		if _, err := os.Stat(oldPath); os.IsNotExist(err) {
			return []models.Report{}, nil
		}

		// Read from old location
		data, err := os.ReadFile(oldPath)
		if err != nil {
			return nil, fmt.Errorf("error reading reports file from old location: %w", err)
		}

		// Migrate to new location
		if err := os.MkdirAll(dp.dataFolder, 0755); err != nil {
			return nil, fmt.Errorf("error creating data directory: %w", err)
		}

		if err := os.WriteFile(reportsFilePath, data, 0644); err != nil {
			return nil, fmt.Errorf("error migrating reports file to new location: %w", err)
		}

		return dp.GetReports()
	}

	// Read file
	data, err := os.ReadFile(reportsFilePath)
	if err != nil {
		return nil, fmt.Errorf("error reading reports file: %w", err)
	}

	// Unmarshal JSON
	var reports []models.Report
	if err := json.Unmarshal(data, &reports); err != nil {
		return nil, fmt.Errorf("error parsing reports file: %w", err)
	}

	// Validate reports and filter out invalid ones
	var validReports []models.Report
	var invalidCount int
	for _, report := range reports {
		if report.ID == "" || report.PresetID == "" {
			invalidCount++
			continue
		}
		validReports = append(validReports, report)
	}

	if invalidCount > 0 {
		log.Printf("Warning: Found %d reports with missing IDs or preset IDs that were filtered out", invalidCount)
	}

	// Only log the total count of reports, not each individual report
	// This prevents excessive logging when there are many reports
	log.Printf("Loaded %d valid reports from storage (filtered out %d invalid reports)", len(validReports), invalidCount)

	// Cache the results - but don't use the cache for now
	// dp.cache.Set(CacheKeyReports, validReports, cache.DefaultExpiration)
	return validReports, nil
}

// DeleteReport deletes a report by ID
func (dp *DataProcessor) DeleteReport(reportID string) error {
	reports, err := dp.GetReports()
	if err != nil {
		return err
	}

	// Find the report to delete
	var reportToDelete models.Report
	var updatedReports []models.Report
	found := false

	for _, report := range reports {
		if report.ID == reportID {
			reportToDelete = report
			found = true
		} else {
			updatedReports = append(updatedReports, report)
		}
	}

	if !found {
		return fmt.Errorf("report not found")
	}

	// Delete the report file
	reportPath := filepath.Join(dp.reportsFolder, reportToDelete.Filename)
	if err := os.Remove(reportPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("error deleting report file: %w", err)
	}

	// Update the reports registry
	reportsFilePath := filepath.Join(dp.dataFolder, reportsFilename)
	data, err := json.MarshalIndent(updatedReports, "", "    ")
	if err != nil {
		return fmt.Errorf("error marshaling reports: %w", err)
	}

	// Write the updated reports to file
	if err := os.WriteFile(reportsFilePath, data, 0644); err != nil {
		return fmt.Errorf("error writing reports file: %w", err)
	}

	// Invalidate cache entries
	dp.cache.Delete(CacheKeyReports)
	dp.cache.Delete(fmt.Sprintf(CacheKeyReport, reportID))

	return nil
}

// BatchDeleteReports deletes multiple reports by their IDs
func (dp *DataProcessor) BatchDeleteReports(reportIDs []string) (models.BatchDeleteResult, error) {
	if len(reportIDs) == 0 {
		return models.BatchDeleteResult{}, fmt.Errorf("no report IDs provided")
	}

	reports, err := dp.GetReports()
	if err != nil {
		return models.BatchDeleteResult{}, err
	}

	result := models.BatchDeleteResult{
		TotalCount:      len(reportIDs),
		SuccessCount:    0,
		FailedCount:     0,
		FailedReportIDs: make(map[string]string),
	}

	// Create a set of report IDs to delete for quick lookup
	reportIDsToDelete := make(map[string]bool)
	for _, reportID := range reportIDs {
		reportIDsToDelete[reportID] = true
	}

	// Create a map to track which report files need to be deleted
	// Use filename as key to avoid deleting the same file multiple times
	reportFilesToDelete := make(map[string]string) // filename -> reportID

	// Create a list of reports to keep
	var updatedReports []models.Report
	for _, report := range reports {
		if reportIDsToDelete[report.ID] {
			// This report should be deleted
			// Add its file to the list of files to delete
			reportFilesToDelete[report.Filename] = report.ID
		} else {
			// Keep this report
			updatedReports = append(updatedReports, report)
		}
	}

	// Delete each report file
	for filename, reportID := range reportFilesToDelete {
		reportPath := filepath.Join(dp.reportsFolder, filename)
		if err := os.Remove(reportPath); err != nil && !os.IsNotExist(err) {
			result.FailedCount++
			result.FailedReportIDs[reportID] = fmt.Sprintf("error deleting report file: %v", err)
		} else {
			result.SuccessCount++
		}
		// Invalidate cache entry for this report
		dp.cache.Delete(fmt.Sprintf(CacheKeyReport, reportID))
	}

	// Update the reports registry
	reportsFilePath := filepath.Join(dp.dataFolder, reportsFilename)
	data, err := json.MarshalIndent(updatedReports, "", "    ")
	if err != nil {
		return result, fmt.Errorf("error marshaling reports: %w", err)
	}

	// Write the updated reports to file
	if err := os.WriteFile(reportsFilePath, data, 0644); err != nil {
		return result, fmt.Errorf("error writing reports file: %w", err)
	}

	// Invalidate reports cache
	dp.cache.Delete(CacheKeyReports)

	// Log the operation for debugging
	log.Printf("Batch delete: %d reports requested, %d reports kept, %d files deleted",
		len(reportIDs), len(updatedReports), result.SuccessCount)

	return result, nil
}

func hasLob(group models.Group, lob string) bool {
	return group.Lob == lob
}

// Helper method for query cache key generation
func (dp *DataProcessor) invalidateUserCache(query models.QueryParams) {
	// Clear any caches related to user queries with this LOB
	if query.LOB != "" {
		dp.ClearCache(fmt.Sprintf("users_lob_%%_%s", query.LOB))
		dp.ClearCache(fmt.Sprintf("users_query_%%_%s", query.LOB))
	} else {
		// If no specific LOB, clear all user-related caches
		dp.ClearCache("users_")
	}
}

// ClearCache clears all cached data or specific cache entries
func (dp *DataProcessor) ClearCache(keyPattern string) {
	if keyPattern == "" {
		// Clear all cache
		dp.cache.Flush()
	} else {
		// Clear cache entries matching the pattern
		// This is a simple implementation - go-cache doesn't support pattern matching directly
		// For advanced pattern matching, use a loop with strings.Contains
		for k := range dp.cache.Items() {
			if strings.Contains(k, keyPattern) {
				dp.cache.Delete(k)
			}
		}
	}
}

// Helper functions for caching

// buildGroupIndices creates search indices for groups to speed up filtering
func (dp *DataProcessor) buildGroupIndices(groups []models.Group) {
	// Acquire write lock for the indices
	dp.indicesMutex.Lock()
	defer dp.indicesMutex.Unlock()

	// Clear existing indices
	dp.nameIndex = make(map[string][]int)
	dp.lobIndex = make(map[string][]int)
	dp.memberIndex = make(map[string][]int)
	dp.descriptionIndex = make(map[string][]int)

	log.Printf("Building search indices for %d groups", len(groups))
	startTime := time.Now()

	// Build indices
	for i, group := range groups {
		// Index by name (lowercase for case-insensitive search)
		name := strings.ToLower(group.Groupname)
		dp.nameIndex[name] = append(dp.nameIndex[name], i)

		// Also index by name parts for partial matching
		nameParts := strings.Fields(name)
		for _, part := range nameParts {
			if part != name { // Avoid duplicating the full name
				dp.nameIndex[part] = append(dp.nameIndex[part], i)
			}
		}

		// Index by LOB
		lob := strings.ToLower(group.Lob)
		if lob != "" {
			dp.lobIndex[lob] = append(dp.lobIndex[lob], i)
		}

		// Index by members
		for _, member := range group.Members {
			memberName := strings.ToLower(member.Name)
			dp.memberIndex[memberName] = append(dp.memberIndex[memberName], i)
		}

		// Index words in description
		if group.Description != "" {
			words := strings.Fields(strings.ToLower(group.Description))
			for _, word := range words {
				// Skip very short words and common words
				if len(word) > 2 {
					dp.descriptionIndex[word] = append(dp.descriptionIndex[word], i)
				}
			}
		}
	}

	elapsed := time.Since(startTime)
	log.Printf("Built indices in %v - Name index: %d entries, LOB index: %d entries, Member index: %d entries, Description index: %d entries",
		elapsed, len(dp.nameIndex), len(dp.lobIndex), len(dp.memberIndex), len(dp.descriptionIndex))
}

// buildUserIndices creates search indices for users to speed up filtering
func (dp *DataProcessor) buildUserIndices(users []models.User) {
	// Acquire write lock for the indices
	dp.indicesMutex.Lock()
	defer dp.indicesMutex.Unlock()

	// Clear existing indices
	dp.userNameIndex = make(map[string][]int)
	dp.userGroupIndex = make(map[string][]int)
	dp.userLobIndex = make(map[string][]int)

	log.Printf("Building search indices for %d users", len(users))
	startTime := time.Now()

	// Build indices
	for i, user := range users {
		// Index by name (lowercase for case-insensitive search)
		name := strings.ToLower(user.Name)
		dp.userNameIndex[name] = append(dp.userNameIndex[name], i)

		// Also index by name parts for partial matching
		nameParts := strings.Fields(name)
		for _, part := range nameParts {
			if part != name { // Avoid duplicating the full name
				dp.userNameIndex[part] = append(dp.userNameIndex[part], i)
			}
		}

		// Index by groups
		for _, group := range user.Groups {
			groupName := strings.ToLower(group)
			dp.userGroupIndex[groupName] = append(dp.userGroupIndex[groupName], i)
		}

		// Index by LOBs
		for _, lob := range user.LOBs {
			lobName := strings.ToLower(lob)
			dp.userLobIndex[lobName] = append(dp.userLobIndex[lobName], i)
		}
	}

	elapsed := time.Since(startTime)
	log.Printf("Built user indices in %v - Name index: %d entries, Group index: %d entries, LOB index: %d entries",
		elapsed, len(dp.userNameIndex), len(dp.userGroupIndex), len(dp.userLobIndex))
}

// GetCachedResult retrieves a cached result by key
func (dp *DataProcessor) GetCachedResult(key string) (interface{}, bool) {
	return dp.cache.Get(key)
}

// SetCachedResult stores a result in the cache
func (dp *DataProcessor) SetCachedResult(key string, value interface{}) {
	dp.cache.Set(key, value, cache.DefaultExpiration)
}

// copyGroups creates a deep copy of groups slice to prevent modification of cached data
func copyGroups(groups []models.Group) []models.Group {
	result := make([]models.Group, len(groups))
	for i, group := range groups {
		newGroup := group
		// Create a deep copy of the Members slice
		newGroup.Members = make(models.GroupMembers, len(group.Members))
		for j, member := range group.Members {
			newGroup.Members[j] = models.Member{
				Name: member.Name,
				Type: member.Type,
			}
		}
		result[i] = newGroup
	}
	return result
}

// getGroupsKey generates a cache key for a slice of groups based on a hash of their content
func getGroupsKey(groups []models.Group) string {
	// Simple hash based on count and a sample of groups
	count := len(groups)
	if count == 0 {
		return "empty"
	}

	// Take a sample of group names (first, middle, last)
	sample := groups[0].Groupname
	if count > 1 {
		sample += groups[count-1].Groupname
	}
	if count > 2 {
		sample += groups[count/2].Groupname
	}

	return fmt.Sprintf("%d_%s", count, sample)
}
