# Default values for adgitops-ui.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Application configuration
app:
  port: 8080
  # NOTE: replicaCount is ignored in deployment.yaml as the application
  # is not designed for horizontal scaling. The value is kept here for
  # reference only. See README.md for more details.
  replicaCount: 1

# Image configuration
image:
  repository: adgitops-ui
  tag: "latest"
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: ClusterIP
  port: 8080

# Resource configuration
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Pod configuration
podAnnotations: {}
nodeSelector: {}
tolerations: []
affinity: {}

# Persistence configuration
persistence:
  enabled: true
  configs:
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 1Gi
  data:
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 5Gi
  reports:
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 5Gi
  repos:
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 10Gi
