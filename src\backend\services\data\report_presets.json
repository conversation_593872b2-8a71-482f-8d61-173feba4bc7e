[{"id": "preset_1746179432348446100", "name": "Test Preset", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:32+02:00", "updatedAt": "2025-05-02T11:50:32+02:00", "isActive": true, "version": 1, "parentId": "preset_1746179432348446100", "sharedId": "shared_1746179432348446100", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1746179432365590100", "name": "Updated Name", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:32+02:00", "updatedAt": "2025-05-02T11:50:32+02:00", "isActive": false, "version": 2, "parentId": "preset_1746179432348446100", "sharedId": "shared_1746179432348446100", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1746179444933310500", "name": "Test Preset", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:44+02:00", "updatedAt": "2025-05-02T11:50:44+02:00", "isActive": true, "version": 1, "parentId": "preset_1746179444933310500", "sharedId": "shared_1746179444933310500", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1746179444948358500", "name": "Updated Name", "description": "Test preset description", "reportType": "users", "query": {"lob": "Marketing", "types": ["security"], "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:44+02:00", "updatedAt": "2025-05-02T11:50:44+02:00", "isActive": false, "version": 2, "parentId": "preset_1746179444933310500", "sharedId": "shared_1746179444933310500", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1746179445035057900", "name": "Marketing Groups", "description": "", "reportType": "groups", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:45+02:00", "updatedAt": "2025-05-02T11:50:45+02:00", "isActive": true, "version": 1, "parentId": "preset_1746179445035057900", "sharedId": "shared_1746179445035057900", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}, {"id": "preset_1746179445067962300", "name": "Toggle Test Preset", "description": "Test preset for toggling activation", "reportType": "users", "query": {"lob": "Marketing", "flattenMembership": false}, "searchQuery": "", "createdAt": "2025-05-02T11:50:45+02:00", "updatedAt": "2025-05-02T11:50:45+02:00", "isActive": true, "version": 1, "parentId": "preset_1746179445067962300", "sharedId": "shared_1746179445067962300", "schedule": {"enabled": false, "frequency": "", "dayOfWeek": 0, "dayOfMonth": 0, "hour": 0, "minute": 0, "intervalHours": 0, "intervalMinutes": 0, "nextRun": ""}, "repositoryId": ""}]