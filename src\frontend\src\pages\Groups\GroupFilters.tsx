import { RefreshCw, FileText } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { GroupSearchHelp } from './GroupSearchHelpUpdated';
import { apiClient } from '@/api/client';
import { Group, PaginationInfo } from './GroupTypes';
import GroupSearchSuggestions from './GroupSearchSuggestions';

interface GroupFiltersProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  selectedLob: string;
  setSelectedLob: (value: string) => void;
  lobs: string[];
  loading: boolean;
  searchInputRef: React.RefObject<HTMLInputElement>;
  selectedRepoId: string | null;
  handleRefreshClick: () => void;
  fetchGroupsData: (repoId: string, page: number, pageSize: number, lob?: string, search?: string) => void;
  pagination: PaginationInfo;
  filteredGroups: Group[];
}

const GroupFilters = ({
  inputValue,
  setInputValue,
  selectedLob,
  setSelectedLob,
  lobs,
  loading,
  searchInputRef,
  selectedRepoId,
  handleRefreshClick,
  fetchGroupsData,
  pagination,
  filteredGroups
}: GroupFiltersProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Handle creating a report preset from the current query
  const handleCreateReportPreset = () => {
    // Navigate to the report presets page with query parameters
    navigate(`/report-presets?createFromQuery=${encodeURIComponent(inputValue)}&reportType=groups`);
  };

  // Handle search submission
  const handleSearch = (value: string) => {
    if (selectedRepoId) {
      // Update URL params
      const params = new URLSearchParams(location.search);

      if (value) {
        // Update both 'query' and 'search' parameters for compatibility
        params.set('query', value);
        params.set('search', value);
      } else {
        params.delete('query');
        params.delete('search');
      }

      // Reset to page 1 when searching
      params.set('page', '1');

      // Update URL without causing a full navigation
      const newUrl = `${location.pathname}?${params.toString()}`;
      window.history.replaceState(null, '', newUrl);

      // Only cancel previous requests if we're actually searching
      if (value) {
        // Cancel any previous requests for groups with the same repository ID
        // This ensures only the latest search query is processed
        const endpoint = `/data/repositories/${selectedRepoId}/groups`;
        const cancelCount = apiClient.utils.cancelRequestsByPattern(endpoint);
        if (cancelCount > 0) {
                  }
      }

      // Fetch data with the new search query
      fetchGroupsData(selectedRepoId, 1, pagination.pageSize, selectedLob, value);
    }
  };

  return (
    <div className="flex items-center w-full">
      <div className="flex-1">
        <GroupSearchSuggestions
          inputValue={inputValue}
          setInputValue={setInputValue}
          onSearch={handleSearch}
          placeholder="Search groups..."
          searchInputRef={searchInputRef}
          groups={filteredGroups}
          lobs={lobs}
        />
      </div>

      <div className="flex items-center space-x-3 ml-3 flex-shrink-0">
        <GroupSearchHelp />

        <Button
          onClick={handleCreateReportPreset}
          disabled={!inputValue.trim()}
          variant="outline"
          size="sm"
          title="Create a report preset from this query"
        >
          <FileText className="h-4 w-4 mr-2" /> Create Preset
        </Button>

        {lobs.length > 0 && (
          <select
            className="border border-gray-300 rounded-md p-1 text-sm bg-white"
            value={selectedLob}
            onChange={(e) => {
              const newLob = e.target.value;
              setSelectedLob(newLob);

              // Update URL params
              const params = new URLSearchParams(location.search);

              if (newLob) {
                params.set('lob', newLob);
              } else {
                params.delete('lob');
              }

              // Reset to page 1 when changing LOB
              params.set('page', '1');

              // Update URL without causing a full navigation
              const newUrl = `${location.pathname}?${params.toString()}`;
              window.history.replaceState(null, '', newUrl);

              // Fetch data with the new LOB filter
              if (selectedRepoId) {
                fetchGroupsData(selectedRepoId, 1, pagination.pageSize, newLob, inputValue);
              }
            }}
          >
            <option value="">All LOBs</option>
            {lobs.map(lob => (
              <option key={lob} value={lob}>{lob}</option>
            ))}
          </select>
        )}

        <Button
          onClick={handleRefreshClick}
          disabled={!selectedRepoId || loading}
          variant="default"
          size="sm"
        >
          <RefreshCw className="h-4 w-4 mr-2" /> Refresh
        </Button>
      </div>
    </div>
  );
};

export default GroupFilters;
