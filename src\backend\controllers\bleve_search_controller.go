package controllers

import (
	"adgitops-ui/src/backend/models"
	"adgitops-ui/src/backend/search"
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// BleveSearchController implements search functionality using Bleve
type BleveSearchController struct {
	dataController *DataController
	searchService  *search.BleveSearchService
}

// NewBleveSearchController creates a new BleveSearchController
func NewBleveSearchController(dataController *DataController, dataDir string) (*BleveSearchController, error) {
	// Create the index path
	indexPath := filepath.Join(dataDir, "search-index")

	// Create the search service
	searchService, err := search.NewBleveSearchService(indexPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create search service: %w", err)
	}

	// Create the controller
	controller := &BleveSearchController{
		dataController: dataController,
		searchService:  searchService,
	}

	// Index the data
	err = controller.indexData(context.Background())
	if err != nil {
		// Close the search service to avoid resource leaks
		searchService.Close()
		return nil, fmt.Errorf("failed to index data: %w", err)
	}

	return controller, nil
}

// indexData indexes all groups and users with repository information
func (c *BleveSearchController) indexData(ctx context.Context) error {
	// Get repository configurations
	configList, err := c.dataController.repoManager.GetConfigurations()
	if err != nil {
		return fmt.Errorf("failed to get repository configurations: %w", err)
	}

	// Clear the index first
	err = c.searchService.ClearIndex(ctx)
	if err != nil {
		return fmt.Errorf("failed to clear index: %w", err)
	}

	// Index data for each repository
	for _, config := range configList.Configs {
		// Get groups for this repository
		groups, err := c.dataController.GetGroupsForRepo(config.ID)
		if err != nil {
			return fmt.Errorf("failed to get groups for repository %s: %w", config.ID, err)
		}

		// Add repository ID to each group
		for i := range groups {
			groups[i].RepoID = config.ID
		}

		// Index the groups
		err = c.searchService.IndexGroups(ctx, groups)
		if err != nil {
			return fmt.Errorf("failed to index groups for repository %s: %w", config.ID, err)
		}

		// Get users for this repository
		users, err := c.dataController.GetUsersForRepo(config.ID)
		if err != nil {
			return fmt.Errorf("failed to get users for repository %s: %w", config.ID, err)
		}

		// Add repository ID to each user
		for i := range users {
			users[i].RepoID = config.ID
		}

		// Index the users
		err = c.searchService.IndexUsers(ctx, users)
		if err != nil {
			return fmt.Errorf("failed to index users for repository %s: %w", config.ID, err)
		}
	}

	return nil
}

// SearchGroups searches for groups matching the query
func (c *BleveSearchController) SearchGroups(query string, repoId string) ([]models.Group, error) {
	// Get all groups
	groups, _ := c.dataController.GetGroupsForRepo(repoId)

	// Search for groups
	return c.searchService.SearchGroups(context.Background(), query, groups)
}

// SearchUsers searches for users matching the query
func (c *BleveSearchController) SearchUsers(query string, repoId string) ([]models.User, error) {
	// Get all users
	users, _ := c.dataController.GetUsersForRepo(repoId)

	// Search for users
	return c.searchService.SearchUsers(context.Background(), query, users)
}

// RegisterRoutes registers the controller's routes
func (c *BleveSearchController) RegisterRoutes(router *gin.RouterGroup) {
	fmt.Printf("Registering search routes\n")

	// Create repository-specific routes
	// Create a generic repository route group with a parameter
	repoRoutes := router.Group("/:repoId/search")
	fmt.Printf("Created repository-specific search routes: %v\n", repoRoutes)
	{
		// Repository-specific search endpoints
		repoRoutes.GET("/groups", c.searchGroupsForRepo)
		repoRoutes.GET("/users", c.searchUsersForRepo)
		repoRoutes.GET("/stats", c.getIndexStatsForRepo)
		repoRoutes.POST("/reindex", c.reindexDataForRepo)
	}

	// Log the registered routes
	fmt.Printf("Registered routes:\n")
	fmt.Printf("  GET /api/{repoId}/search/groups\n")
	fmt.Printf("  GET /api/{repoId}/search/users\n")
	fmt.Printf("  GET /api/{repoId}/search/stats\n")
	fmt.Printf("  POST /api/{repoId}/search/reindex\n")

	fmt.Printf("Search routes registered\n")
}

// searchGroupsForRepo handles the repository-specific group search endpoint
func (c *BleveSearchController) searchGroupsForRepo(ctx *gin.Context) {
	// Log the request
	fmt.Printf("searchGroupsForRepo: received request with URL %s\n", ctx.Request.URL.String())

	// Get the repository ID from the URL path
	repoId := ctx.Param("repoId")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}

	// Get query parameters
	searchQuery := ctx.Query("query")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "20"))
	enhanced := ctx.Query("enhanced")

	// Log the query parameters
	fmt.Printf("searchGroupsForRepo: repoId=%s, query=%s, page=%d, pageSize=%d, enhanced=%s\n", repoId, searchQuery, page, pageSize, enhanced)

	// Add repository filter to the query
	// Apply exact matching for all field-specific searches
	// This regex matches field:value patterns, capturing both the field name and value
	fieldRegex := regexp.MustCompile(`(\w+):([^\s"]+|"[^"]+")`)
	matches := fieldRegex.FindAllStringSubmatch(searchQuery, -1)

	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		fieldName := match[1]
		fieldValue := match[2]
		originalMatch := match[0]

		// Skip if it already has wildcards or it's a special field
		if strings.Contains(fieldValue, "*") || strings.Contains(fieldValue, "~") ||
			fieldName == "_type" || strings.HasPrefix(fieldValue, "(") {
			continue
		}

		// Remove quotes if present
		fieldValue = strings.Trim(fieldValue, "\"")

		// Special handling for name and lob fields
		if fieldName == "name" || fieldName == "lob" || fieldName == "groupname" {
			// Replace with exact match (add quotes)
			searchQuery = strings.Replace(searchQuery, originalMatch, fmt.Sprintf("%s:\"%s\"", fieldName, fieldValue), 1)
			fmt.Printf("searchGroupsForRepo: converted %s search to exact match: %s -> %s\n", fieldName, originalMatch, fmt.Sprintf("%s:\"%s\"", fieldName, fieldValue))
		} else {
			// For other fields, add wildcards for partial matching
			searchQuery = strings.Replace(searchQuery, originalMatch, fmt.Sprintf("%s:*%s*", fieldName, fieldValue), 1)
			fmt.Printf("searchGroupsForRepo: converted %s search to partial match: %s -> %s\n", fieldName, originalMatch, fmt.Sprintf("%s:*%s*", fieldName, fieldValue))
		}
	}

	// Add repository filter to the query
	if searchQuery != "" {
		searchQuery = fmt.Sprintf("(%s) AND repoId:%s", searchQuery, repoId)
	} else {
		// When no query is provided, we want to return all groups for this repository
		// We'll use a special query that matches all documents with the given repository ID
		searchQuery = fmt.Sprintf("_all:true AND repoId:%s", repoId)
	}

	// Search for groups
	groups, err := c.SearchGroups(searchQuery, repoId)
	if err != nil {
		// Check if it's a syntax error
		if strings.Contains(err.Error(), "invalid syntax") {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to search groups: %v", err)})
		}
		return
	}

	// Check if enhanced membership information is requested
	if enhanced == "true" {
		log.Printf("Enhanced membership requested for %d groups", len(groups))

		// Check if dataController and dataProcessor are available
		if c.dataController == nil {
			log.Printf("ERROR: dataController is nil")
		} else if c.dataController.dataProcessor == nil {
			log.Printf("ERROR: dataProcessor is nil")
		} else {
			log.Printf("Calling EnrichMembershipInfo...")
			// Apply enhanced membership enrichment
			c.dataController.dataProcessor.EnrichMembershipInfo(groups)
			log.Printf("Enhanced membership processing completed")

			// Debug: Print the first group's enhanced data
			if len(groups) > 0 {
				group := groups[0]
				log.Printf("Group %s enhanced data:", group.Groupname)
				if group.DirectMembers != nil {
					log.Printf("  DirectMembers: %d users, %d groups", len(group.DirectMembers.Users), len(group.DirectMembers.Groups))
				} else {
					log.Printf("  DirectMembers: nil")
				}
				if group.ResolvedMembers != nil {
					log.Printf("  ResolvedMembers: %d users", len(group.ResolvedMembers.Users))
				} else {
					log.Printf("  ResolvedMembers: nil")
				}
				log.Printf("  ParentGroups: %v", group.ParentGroups)
			}
		}
	}

	// Calculate pagination
	total := len(groups)
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	// Paginate results
	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= total {
		start = 0
		end = 0
	}
	if end > total {
		end = total
	}

	paginatedGroups := groups
	if start < end {
		paginatedGroups = groups[start:end]
	} else {
		paginatedGroups = []models.Group{}
	}

	// Return results
	ctx.JSON(http.StatusOK, gin.H{
		"groups":     paginatedGroups,
		"total":      total,
		"page":       page,
		"pageSize":   pageSize,
		"totalPages": totalPages,
	})
}

// searchUsersForRepo handles the repository-specific user search endpoint
func (c *BleveSearchController) searchUsersForRepo(ctx *gin.Context) {
	// Log the request
	fmt.Printf("searchUsersForRepo: received request with URL %s\n", ctx.Request.URL.String())

	// Get the repository ID from the URL path
	repoId := ctx.Param("repoId")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}

	// Get query parameters
	searchQuery := ctx.Query("query")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "20"))

	// Log the query parameters
	fmt.Printf("searchUsersForRepo: repoId=%s, query=%s, page=%d, pageSize=%d\n", repoId, searchQuery, page, pageSize)

	// Add repository filter to the query
	// Apply exact matching for all field-specific searches
	// This regex matches field:value patterns, capturing both the field name and value
	fieldRegex := regexp.MustCompile(`(\w+):([^\s"]+|"[^"]+")`)
	matches := fieldRegex.FindAllStringSubmatch(searchQuery, -1)

	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		fieldName := match[1]
		fieldValue := match[2]
		originalMatch := match[0]

		// Skip if it already has wildcards or it's a special field
		if strings.Contains(fieldValue, "*") || strings.Contains(fieldValue, "~") ||
			fieldName == "_type" || strings.HasPrefix(fieldValue, "(") {
			continue
		}

		// Remove quotes if present
		fieldValue = strings.Trim(fieldValue, "\"")

		// Special handling for name and lob fields
		if fieldName == "name" || fieldName == "lob" || fieldName == "groupname" {
			// Replace with exact match (add quotes)
			searchQuery = strings.Replace(searchQuery, originalMatch, fmt.Sprintf("%s:\"%s\"", fieldName, fieldValue), 1)
			fmt.Printf("searchUsersForRepo: converted %s search to exact match: %s -> %s\n", fieldName, originalMatch, fmt.Sprintf("%s:\"%s\"", fieldName, fieldValue))
		} else {
			// For other fields, add wildcards for partial matching
			searchQuery = strings.Replace(searchQuery, originalMatch, fmt.Sprintf("%s:*%s*", fieldName, fieldValue), 1)
			fmt.Printf("searchUsersForRepo: converted %s search to partial match: %s -> %s\n", fieldName, originalMatch, fmt.Sprintf("%s:*%s*", fieldName, fieldValue))
		}
	}

	// Add repository filter to the query
	if searchQuery != "" {
		searchQuery = fmt.Sprintf("(%s) AND repoId:%s", searchQuery, repoId)
	} else {
		// When no query is provided, we want to return all users for this repository
		// We'll use a special query that matches all documents with the given repository ID
		searchQuery = fmt.Sprintf("_all:true AND repoId:%s", repoId)
	}

	// Search for users
	users, err := c.SearchUsers(searchQuery, repoId)
	if err != nil {
		// Check if it's a syntax error
		if strings.Contains(err.Error(), "invalid syntax") {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to search users: %v", err)})
		}
		return
	}

	// Calculate pagination
	total := len(users)
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	// Paginate results
	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= total {
		start = 0
		end = 0
	}
	if end > total {
		end = total
	}

	paginatedUsers := users
	if start < end {
		paginatedUsers = users[start:end]
	} else {
		paginatedUsers = []models.User{}
	}

	// Return results
	ctx.JSON(http.StatusOK, gin.H{
		"users":      paginatedUsers,
		"total":      total,
		"page":       page,
		"pageSize":   pageSize,
		"totalPages": totalPages,
	})
}

// getIndexStatsForRepo returns statistics about the search index for a specific repository
func (c *BleveSearchController) getIndexStatsForRepo(ctx *gin.Context) {
	// Log the request
	fmt.Printf("getIndexStatsForRepo: received request with URL %s\n", ctx.Request.URL.String())

	// Get the repository ID from the URL path
	repoId := ctx.Param("repoId")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}

	// Log the repository ID
	fmt.Printf("getIndexStatsForRepo: repoId=%s\n", repoId)

	// Get index stats for this repository
	stats, err := c.searchService.GetIndexStats(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get index stats: %v", err)})
		return
	}

	// Return stats
	ctx.JSON(http.StatusOK, stats)
}

// reindexDataForRepo reindexes data for a specific repository
func (c *BleveSearchController) reindexDataForRepo(ctx *gin.Context) {
	// Log the request
	fmt.Printf("reindexDataForRepo: received request with URL %s\n", ctx.Request.URL.String())

	// Get the repository ID from the URL path
	repoId := ctx.Param("repoId")
	if repoId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Repository ID is required"})
		return
	}

	// Log the repository ID
	fmt.Printf("reindexDataForRepo: repoId=%s\n", repoId)

	// Get repository configuration
	repo, err := c.dataController.repoManager.GetRepository(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get repository: %v", err)})
		return
	}
	if repo == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Repository not found: %s", repoId)})
		return
	}

	// Get groups for this repository
	groups, err := c.dataController.GetGroupsForRepo(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get groups: %v", err)})
		return
	}

	// Add repository ID to each group
	for i := range groups {
		groups[i].RepoID = repoId
	}

	// Index the groups
	err = c.searchService.IndexGroups(ctx, groups)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to index groups: %v", err)})
		return
	}

	// Get users for this repository
	users, err := c.dataController.GetUsersForRepo(repoId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get users: %v", err)})
		return
	}

	// Add repository ID to each user
	for i := range users {
		users[i].RepoID = repoId
	}

	// Index the users
	err = c.searchService.IndexUsers(ctx, users)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to index users: %v", err)})
		return
	}

	// Return success
	ctx.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("Data for repository %s reindexed successfully", repoId)})
}

// Close closes the search service
func (c *BleveSearchController) Close() error {
	return c.searchService.Close()
}
